# Enhanced Long-Term Memory System

## Overview

The enhanced long-term memory system provides robust, user-specific memory isolation with role-based memory matching and advanced retrieval capabilities using Weaviate vector storage. This system ensures that each user has their own separate memory space while supporting contextual memory retrieval based on semantic similarity and user/role context.

## Key Features

### 1. User-Specific Memory Isolation
- **Complete Data Separation**: Each user has their own isolated memory space
- **Cross-User Data Protection**: Prevents accidental data leakage between users
- **Organization-Level Grouping**: Users within the same organization can be grouped while maintaining individual isolation
- **Backward Compatibility**: Existing organization-only isolation continues to work

### 2. Role-Based Memory Matching
- **Role-Aware Storage**: Messages are stored with role metadata (user, assistant, system)
- **Role-Specific Retrieval**: Query memories by specific roles
- **Role-Based Scoring**: Different roles receive different relevance scores
- **Context Preservation**: Maintains conversation context across different roles

### 3. Memory Segmentation
- **User-Organization Keys**: Composite keys for precise data partitioning
- **Memory Type Classification**: Support for different memory types (short-term, long-term)
- **Conversation Isolation**: Separate memories by conversation ID
- **Metadata-Rich Storage**: Enhanced metadata for better filtering and retrieval

### 4. Enhanced Retrieval Logic
- **Semantic Similarity**: Vector-based similarity search using embeddings
- **User Context Scoring**: Bonus scoring for same-user memories
- **Role-Based Weighting**: Different importance weights for different roles
- **Time Decay Factors**: Recent memories receive higher relevance scores
- **Configurable Thresholds**: Adjustable minimum relevance scores

## Architecture

### Core Components

1. **VectorStoreRetriever**: Enhanced memory retriever with user isolation
2. **Multitenancy Context**: Extended context management for user and organization IDs
3. **Weaviate Integration**: Enhanced vector store with user-specific class naming
4. **Memory Interfaces**: Extended interfaces supporting new retrieval options

### Data Flow

```
User Request → Context (User ID + Org ID) → Memory Storage/Retrieval → User-Isolated Results
```

### Storage Structure

```
Document ID: {user_org_key}-{role}-{conversation_id}-{timestamp}
Metadata: {
  "role": "user|assistant|system",
  "timestamp": unix_timestamp,
  "org_id": "organization_id",
  "user_id": "user_id", 
  "conversation_id": "conversation_id",
  "user_org_key": "org_id:user_id",
  "memory_type": "long_term"
}
```

## Usage Examples

### Basic User-Isolated Memory Storage

```go
import (
    "context"
    "github.com/run-bigpig/hongdou/internal/pkg/memory"
    "github.com/run-bigpig/hongdou/internal/pkg/multitenancy"
    "github.com/run-bigpig/hongdou/internal/pkg/interfaces"
)

// Create context with user isolation
ctx := context.Background()
ctx = multitenancy.WithOrgID(ctx, "my-org")
ctx = multitenancy.WithUserID(ctx, "user-123")
ctx = memory.WithConversationID(ctx, "conv-456")

// Create memory retriever
retriever := memory.NewVectorStoreRetriever(vectorStore)

// Store user message
message := interfaces.Message{
    Role:    "user",
    Content: "I prefer coffee over tea",
    Metadata: map[string]interface{}{
        "timestamp": time.Now().Unix(),
        "topic":     "preferences",
    },
}

err := retriever.AddMessage(ctx, message)
```

### Role-Based Memory Retrieval

```go
// Retrieve only assistant responses
assistantMemories, err := retriever.GetMessages(ctx,
    interfaces.WithQuery("coffee preferences"),
    interfaces.WithRoles("assistant"),
    interfaces.WithLimit(10),
)

// Retrieve system messages
systemMemories, err := retriever.GetMessages(ctx,
    interfaces.WithRoles("system"),
    interfaces.WithLimit(5),
)
```

### Enhanced Retrieval with Scoring

```go
// Retrieve memories with enhanced scoring and filtering
memories, err := retriever.GetMessages(ctx,
    interfaces.WithQuery("user preferences and history"),
    interfaces.WithIncludeScore(true),
    interfaces.WithMemoryMinScore(0.3),
    interfaces.WithMemoryType("long_term"),
    interfaces.WithLimit(20),
)

// Access enhanced scores
for _, msg := range memories {
    baseScore := msg.Metadata["score"]
    enhancedScore := msg.Metadata["enhanced_score"]
    fmt.Printf("Base: %v, Enhanced: %v\n", baseScore, enhancedScore)
}
```

### Time-Based Memory Filtering

```go
// Retrieve recent memories (last 24 hours)
yesterday := time.Now().Add(-24 * time.Hour).Unix()
now := time.Now().Unix()

recentMemories, err := retriever.GetMessages(ctx,
    interfaces.WithQuery("recent interactions"),
    interfaces.WithTimeRange(yesterday, now),
    interfaces.WithLimit(15),
)
```

## Configuration

### Vector Store Configuration

```go
// Enhanced Weaviate configuration with user isolation
config := &interfaces.VectorStoreConfig{
    Host:           "localhost:8080",
    Scheme:         "http",
    ClassPrefix:    "Memory",
    DistanceMetric: "cosine",
}

store := weaviate.New(config,
    weaviate.WithClassPrefix("UserMemory"),
    weaviate.WithEmbedder(embedder),
)
```

### Memory Retriever Options

```go
// Create retriever with custom options
retriever := memory.NewVectorStoreRetriever(
    vectorStore,
    // Add custom options here if needed
)
```

## Enhanced Scoring Algorithm

The system uses a multi-factor scoring algorithm:

1. **Base Semantic Score**: Vector similarity score (0.0 - 1.0)
2. **User Context Bonus**: +0.2 for same user, +0.1 for same organization
3. **Role-Based Bonus**: +0.15 for system, +0.1 for assistant, +0.05 for user
4. **Time Decay Bonus**: +0.1 for recent (< 1 day), +0.05 for week-old
5. **Final Score**: Capped at 1.0

## Security and Isolation

### Data Isolation Guarantees

- **User-Level Isolation**: Users cannot access other users' memories
- **Organization Boundaries**: Cross-organization access is prevented
- **Conversation Separation**: Memories are isolated by conversation ID
- **Metadata Protection**: User context is embedded in all stored documents

### Access Control

- **Context-Based Access**: All operations require proper user context
- **Automatic Filtering**: User isolation filters are automatically applied
- **Fallback Mechanisms**: Graceful degradation when context is missing

## Migration and Backward Compatibility

### Existing Data

- **Automatic Migration**: Existing memories work with organization-only keys
- **Gradual Enhancement**: New user context is added as users interact
- **Fallback Support**: System works with partial context information

### API Compatibility

- **Extended Interfaces**: New options are additive, existing code continues to work
- **Optional Features**: Enhanced features are opt-in through new options
- **Deprecation Path**: Clear migration path for deprecated features

## Performance Considerations

### Optimization Strategies

- **Efficient Indexing**: User-org keys enable efficient data partitioning
- **Batch Operations**: Bulk storage and retrieval operations
- **Caching**: Context-aware caching for frequently accessed memories
- **Connection Pooling**: Optimized vector store connections

### Scalability

- **Horizontal Scaling**: User-based partitioning supports horizontal scaling
- **Memory Management**: Configurable memory limits and cleanup policies
- **Load Balancing**: User-aware load distribution

## Monitoring and Observability

### Metrics

- **Memory Usage**: Per-user and per-organization memory consumption
- **Retrieval Performance**: Query latency and relevance scores
- **Storage Efficiency**: Vector store utilization and optimization
- **User Activity**: Memory access patterns and usage statistics

### Logging

- **Context Logging**: All operations include user and organization context
- **Performance Logging**: Detailed timing and scoring information
- **Error Tracking**: Enhanced error reporting with user context

## Future Enhancements

### Planned Features

- **Memory Summarization**: Automatic summarization of old memories
- **Cross-User Insights**: Privacy-preserving insights across users
- **Advanced Filtering**: More sophisticated query and filtering options
- **Memory Analytics**: Detailed analytics and reporting capabilities

### Extensibility

- **Plugin Architecture**: Support for custom memory processors
- **Custom Scoring**: Pluggable scoring algorithms
- **External Integrations**: Integration with external knowledge bases
- **Multi-Modal Support**: Support for non-text memory types
