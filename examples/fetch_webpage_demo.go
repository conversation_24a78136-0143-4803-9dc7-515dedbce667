package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/run-bigpig/hongdou/internal/service/tools/fetch_webpage"
)

// This example demonstrates how to use the fetch_webpage tool
func main() {
	fmt.Println("=== Fetch Webpage Tool Demo ===\n")

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Create the webpage fetching tool
	tool := fetch_webpage.New(ctx)

	// Display tool information
	fmt.Printf("Tool Name: %s\n", tool.Name())
	fmt.Printf("Tool Description: %s\n", tool.Description())
	fmt.Println()

	// Display tool parameters
	fmt.Println("Tool Parameters:")
	params := tool.Parameters()
	for name, spec := range params {
		fmt.Printf("  - %s (%s): %s [Required: %v]\n", 
			name, spec.Type, spec.Description, spec.Required)
	}
	fmt.Println()

	// Test URLs to demonstrate the tool
	testURLs := []string{
		"https://httpbin.org/html",           // Simple HTML test page
		"https://example.com",                // Basic example site
		"github.com",                         // URL without protocol
		"https://httpbin.org/json",           // Non-HTML content
		"https://nonexistent-site-12345.com", // Error case
	}

	// Test each URL
	for i, url := range testURLs {
		fmt.Printf("=== Test %d: %s ===\n", i+1, url)
		
		result, err := tool.Run(ctx, url)
		if err != nil {
			fmt.Printf("Error: %v\n", err)
		} else {
			fmt.Printf("Result:\n%s\n", result)
		}
		fmt.Println()
		
		// Add a small delay between requests to be respectful
		time.Sleep(1 * time.Second)
	}

	// Demonstrate Execute method (same as Run)
	fmt.Println("=== Testing Execute Method ===")
	result, err := tool.Execute(ctx, "https://httpbin.org/html")
	if err != nil {
		log.Printf("Execute method error: %v", err)
	} else {
		fmt.Printf("Execute method result:\n%s\n", result)
	}

	fmt.Println("=== Demo Complete ===")
}
