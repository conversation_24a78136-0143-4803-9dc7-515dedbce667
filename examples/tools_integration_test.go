package main

import (
	"context"
	"fmt"
	"log"

	"github.com/run-bigpig/hongdou/internal/config"
	"github.com/run-bigpig/hongdou/internal/service/tools"
)

// This example demonstrates that the fetch_webpage tool is properly integrated
// into the tools system and can be discovered and used
func main() {
	fmt.Println("=== Tools Integration Test ===\n")

	// Create context
	ctx := context.Background()

	// Create tools system with empty MCP config for testing
	mcpConfig := config.McpServerConfig{
		Host: "http://localhost:8080", // This won't be used in this test
	}

	// Initialize tools system
	toolsSystem := tools.NewTools(ctx, mcpConfig)

	// Test that our new tool is registered
	fmt.Println("Testing tool registration...")

	// List specific tools including our new one
	toolNames := []string{"get_weather", "fetch_webpage"}
	registeredTools := toolsSystem.List(toolNames...)

	fmt.Printf("Requested %d tools, got %d tools\n", len(toolNames), len(registeredTools))

	// Display information about each tool
	for i, tool := range registeredTools {
		fmt.Printf("\nTool %d:\n", i+1)
		fmt.Printf("  Name: %s\n", tool.Name())
		fmt.Printf("  Description: %s\n", tool.Description())
		
		// Display parameters
		params := tool.Parameters()
		fmt.Printf("  Parameters (%d):\n", len(params))
		for name, spec := range params {
			fmt.Printf("    - %s (%s): %s [Required: %v]\n", 
				name, spec.Type, spec.Description, spec.Required)
		}
	}

	// Test tool discovery (simple keyword matching)
	fmt.Println("\n=== Testing Tool Discovery ===")
	
	queries := []string{
		"webpage",
		"fetch",
		"URL",
		"网页",
		"weather",
		"天气",
	}

	for _, query := range queries {
		fmt.Printf("\nQuery: '%s'\n", query)
		discoveredTools, err := toolsSystem.DiscoverTools(query, 3)
		if err != nil {
			log.Printf("Error discovering tools for query '%s': %v", query, err)
			continue
		}

		fmt.Printf("Found %d tools:\n", len(discoveredTools))
		for j, tool := range discoveredTools {
			fmt.Printf("  %d. %s - %s\n", j+1, tool.Name(), tool.Description())
		}
	}

	// Test tools context generation
	fmt.Println("\n=== Testing Tools Context Generation ===")
	
	contextQueries := []string{
		"I need to fetch webpage content",
		"What's the weather like?",
		"Help me get information from a website",
	}

	for _, query := range contextQueries {
		fmt.Printf("\nQuery: '%s'\n", query)
		context := toolsSystem.GetToolsContext(query, 2)
		fmt.Printf("Generated context:\n%s\n", context)
	}

	fmt.Println("=== Integration Test Complete ===")
}
