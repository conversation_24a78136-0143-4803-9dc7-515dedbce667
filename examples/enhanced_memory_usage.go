package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/run-bigpig/hongdou/internal/pkg/memory"
	"github.com/run-bigpig/hongdou/internal/pkg/multitenancy"
)

// This example demonstrates the enhanced long-term memory functionality
// with user-specific isolation, role-based memory matching, and enhanced retrieval

func main() {
	// Example 1: User-Specific Memory Isolation
	fmt.Println("=== Example 1: User-Specific Memory Isolation ===")
	demonstrateUserIsolation()

	// Example 2: Role-Based Memory Matching
	fmt.Println("\n=== Example 2: Role-Based Memory Matching ===")
	demonstrateRoleBasedRetrieval()

	// Example 3: Enhanced Retrieval with Scoring
	fmt.Println("\n=== Example 3: Enhanced Retrieval with Scoring ===")
	demonstrateEnhancedRetrieval()

	// Example 4: Time-Based Memory Filtering
	fmt.Println("\n=== Example 4: Time-Based Memory Filtering ===")
	demonstrateTimeBasedFiltering()
}

func demonstrateUserIsolation() {
	// Create a mock vector store (in real usage, you'd use Weaviate)
	mockStore := &MockVectorStore{}
	retriever := memory.NewVectorStoreRetriever(mockStore)

	// Create contexts for different users in the same organization
	user1Ctx := createUserContext("org1", "user1", "conversation1")
	user2Ctx := createUserContext("org1", "user2", "conversation2")

	// Add memories for user 1
	user1Messages := []interfaces.Message{
		{
			Role:    "user",
			Content: "I prefer coffee over tea",
			Metadata: map[string]interface{}{
				"timestamp": time.Now().Unix(),
				"topic":     "preferences",
			},
		},
		{
			Role:    "assistant",
			Content: "I'll remember that you prefer coffee. Would you like me to suggest coffee shops?",
			Metadata: map[string]interface{}{
				"timestamp": time.Now().Unix(),
				"topic":     "preferences",
			},
		},
	}

	for _, msg := range user1Messages {
		if err := retriever.AddMessage(user1Ctx, msg); err != nil {
			log.Printf("Error adding message for user1: %v", err)
		}
	}

	// Add memories for user 2
	user2Messages := []interfaces.Message{
		{
			Role:    "user",
			Content: "I'm allergic to coffee, I only drink tea",
			Metadata: map[string]interface{}{
				"timestamp": time.Now().Unix(),
				"topic":     "preferences",
			},
		},
		{
			Role:    "assistant",
			Content: "I'll note your coffee allergy. I can recommend great tea varieties instead.",
			Metadata: map[string]interface{}{
				"timestamp": time.Now().Unix(),
				"topic":     "preferences",
			},
		},
	}

	for _, msg := range user2Messages {
		if err := retriever.AddMessage(user2Ctx, msg); err != nil {
			log.Printf("Error adding message for user2: %v", err)
		}
	}

	// Retrieve memories for user 1 - should only get user 1's memories
	user1Memories, err := retriever.GetMessages(user1Ctx,
		interfaces.WithQuery("coffee preferences"),
		interfaces.WithLimit(10),
		interfaces.WithIncludeScore(true),
	)

	if err != nil {
		log.Printf("Error retrieving user1 memories: %v", err)
		return
	}

	fmt.Printf("User 1 retrieved %d memories about coffee preferences\n", len(user1Memories))
	for i, msg := range user1Memories {
		fmt.Printf("  Memory %d: %s (Role: %s)\n", i+1, msg.Content, msg.Role)
	}

	// Retrieve memories for user 2 - should only get user 2's memories
	user2Memories, err := retriever.GetMessages(user2Ctx,
		interfaces.WithQuery("coffee preferences"),
		interfaces.WithLimit(10),
		interfaces.WithIncludeScore(true),
	)

	if err != nil {
		log.Printf("Error retrieving user2 memories: %v", err)
		return
	}

	fmt.Printf("User 2 retrieved %d memories about coffee preferences\n", len(user2Memories))
	for i, msg := range user2Memories {
		fmt.Printf("  Memory %d: %s (Role: %s)\n", i+1, msg.Content, msg.Role)
	}
}

func demonstrateRoleBasedRetrieval() {
	mockStore := &MockVectorStore{}
	retriever := memory.NewVectorStoreRetriever(mockStore)

	ctx := createUserContext("org1", "user1", "conversation1")

	// Add messages with different roles
	messages := []interfaces.Message{
		{
			Role:    "system",
			Content: "You are a helpful AI assistant specialized in cooking advice",
			Metadata: map[string]interface{}{
				"timestamp": time.Now().Unix(),
				"type":      "system_prompt",
			},
		},
		{
			Role:    "user",
			Content: "What's a good recipe for pasta?",
			Metadata: map[string]interface{}{
				"timestamp": time.Now().Unix(),
			},
		},
		{
			Role:    "assistant",
			Content: "Here's a simple spaghetti carbonara recipe...",
			Metadata: map[string]interface{}{
				"timestamp": time.Now().Unix(),
			},
		},
		{
			Role:    "user",
			Content: "Can you make it vegetarian?",
			Metadata: map[string]interface{}{
				"timestamp": time.Now().Unix(),
			},
		},
		{
			Role:    "assistant",
			Content: "Absolutely! Here's a vegetarian version using mushrooms instead of bacon...",
			Metadata: map[string]interface{}{
				"timestamp": time.Now().Unix(),
			},
		},
	}

	for _, msg := range messages {
		if err := retriever.AddMessage(ctx, msg); err != nil {
			log.Printf("Error adding message: %v", err)
		}
	}

	// Retrieve only assistant responses
	assistantMemories, err := retriever.GetMessages(ctx,
		interfaces.WithQuery("recipe cooking"),
		interfaces.WithRoles("assistant"),
		interfaces.WithLimit(10),
	)

	if err != nil {
		log.Printf("Error retrieving assistant memories: %v", err)
		return
	}

	fmt.Printf("Retrieved %d assistant memories about cooking:\n", len(assistantMemories))
	for i, msg := range assistantMemories {
		fmt.Printf("  Assistant Memory %d: %s\n", i+1, msg.Content[:50]+"...")
	}

	// Retrieve only user questions
	userMemories, err := retriever.GetMessages(ctx,
		interfaces.WithQuery("recipe cooking"),
		interfaces.WithRoles("user"),
		interfaces.WithLimit(10),
	)

	if err != nil {
		log.Printf("Error retrieving user memories: %v", err)
		return
	}

	fmt.Printf("Retrieved %d user memories about cooking:\n", len(userMemories))
	for i, msg := range userMemories {
		fmt.Printf("  User Memory %d: %s\n", i+1, msg.Content)
	}
}

func demonstrateEnhancedRetrieval() {
	mockStore := &MockVectorStore{}
	retriever := memory.NewVectorStoreRetriever(mockStore)

	ctx := createUserContext("org1", "user1", "conversation1")

	// Add messages with varying importance and recency
	messages := []interfaces.Message{
		{
			Role:    "user",
			Content: "My name is Alice and I work as a software engineer",
			Metadata: map[string]interface{}{
				"timestamp":  time.Now().Add(-24 * time.Hour).Unix(), // 1 day ago
				"importance": "high",
				"topic":      "personal_info",
			},
		},
		{
			Role:    "assistant",
			Content: "Nice to meet you Alice! I'll remember that you're a software engineer.",
			Metadata: map[string]interface{}{
				"timestamp":  time.Now().Add(-24 * time.Hour).Unix(),
				"importance": "high",
				"topic":      "personal_info",
			},
		},
		{
			Role:    "user",
			Content: "I'm working on a Python project using Django",
			Metadata: map[string]interface{}{
				"timestamp":  time.Now().Add(-2 * time.Hour).Unix(), // 2 hours ago
				"importance": "medium",
				"topic":      "work",
			},
		},
		{
			Role:    "user",
			Content: "What's the weather like today?",
			Metadata: map[string]interface{}{
				"timestamp":  time.Now().Add(-30 * time.Minute).Unix(), // 30 min ago
				"importance": "low",
				"topic":      "casual",
			},
		},
	}

	for _, msg := range messages {
		if err := retriever.AddMessage(ctx, msg); err != nil {
			log.Printf("Error adding message: %v", err)
		}
	}

	// Retrieve memories with enhanced scoring
	memories, err := retriever.GetMessages(ctx,
		interfaces.WithQuery("Alice software engineer Python"),
		interfaces.WithIncludeScore(true),
		interfaces.WithMemoryMinScore(0.1),
		interfaces.WithLimit(10),
	)

	if err != nil {
		log.Printf("Error retrieving memories: %v", err)
		return
	}

	fmt.Printf("Retrieved %d memories with enhanced scoring:\n", len(memories))
	for i, msg := range memories {
		score := msg.Metadata["score"]
		enhancedScore := msg.Metadata["enhanced_score"]
		fmt.Printf("  Memory %d: %s\n", i+1, msg.Content)
		fmt.Printf("    Base Score: %v, Enhanced Score: %v\n", score, enhancedScore)
	}
}

func demonstrateTimeBasedFiltering() {
	mockStore := &MockVectorStore{}
	retriever := memory.NewVectorStoreRetriever(mockStore)

	ctx := createUserContext("org1", "user1", "conversation1")

	// Add messages across different time periods
	now := time.Now()
	timePoints := []time.Time{
		now.Add(-7 * 24 * time.Hour), // 1 week ago
		now.Add(-3 * 24 * time.Hour), // 3 days ago
		now.Add(-1 * time.Hour),      // 1 hour ago
		now.Add(-10 * time.Minute),   // 10 minutes ago
	}

	for i, t := range timePoints {
		msg := interfaces.Message{
			Role:    "user",
			Content: fmt.Sprintf("Message from %s", t.Format("2006-01-02 15:04")),
			Metadata: map[string]interface{}{
				"timestamp": t.Unix(),
			},
		}

		if err := retriever.AddMessage(ctx, msg); err != nil {
			log.Printf("Error adding message %d: %v", i, err)
		}
	}

	// Retrieve only recent memories (last 2 days)
	twoDaysAgo := now.Add(-2 * 24 * time.Hour).Unix()
	recentMemories, err := retriever.GetMessages(ctx,
		interfaces.WithQuery("message"),
		interfaces.WithTimeRange(twoDaysAgo, now.Unix()),
		interfaces.WithLimit(10),
	)

	if err != nil {
		log.Printf("Error retrieving recent memories: %v", err)
		return
	}

	fmt.Printf("Retrieved %d recent memories (last 2 days):\n", len(recentMemories))
	for i, msg := range recentMemories {
		timestamp := msg.Metadata["timestamp"]
		fmt.Printf("  Recent Memory %d: %s (Timestamp: %v)\n", i+1, msg.Content, timestamp)
	}
}

func createUserContext(orgID, userID, conversationID string) context.Context {
	ctx := context.Background()
	ctx = multitenancy.WithOrgID(ctx, orgID)
	ctx = multitenancy.WithUserID(ctx, userID)
	ctx = memory.WithConversationID(ctx, conversationID)
	return ctx
}

// MockVectorStore for demonstration purposes
type MockVectorStore struct {
	documents []interfaces.Document
}

func (m *MockVectorStore) Store(ctx context.Context, documents []interfaces.Document, options ...interfaces.StoreOption) error {
	m.documents = append(m.documents, documents...)
	return nil
}

func (m *MockVectorStore) Search(ctx context.Context, query string, limit int, options ...interfaces.SearchOption) ([]interfaces.SearchResult, error) {
	var results []interfaces.SearchResult
	for i, doc := range m.documents {
		if i >= limit {
			break
		}
		results = append(results, interfaces.SearchResult{
			Document: doc,
			Score:    0.8, // Mock score
		})
	}
	return results, nil
}

func (m *MockVectorStore) SearchByVector(ctx context.Context, vector []float32, limit int, options ...interfaces.SearchOption) ([]interfaces.SearchResult, error) {
	return m.Search(ctx, "vector_search", limit, options...)
}

func (m *MockVectorStore) Delete(ctx context.Context, ids []string, options ...interfaces.DeleteOption) error {
	return nil
}

func (m *MockVectorStore) Get(ctx context.Context, ids []string) ([]interfaces.Document, error) {
	return nil, nil
}
