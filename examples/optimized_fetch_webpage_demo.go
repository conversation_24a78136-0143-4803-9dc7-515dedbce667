package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/run-bigpig/hongdou/internal/service/tools/fetch_webpage"
)

// This example demonstrates the optimized fetch_webpage tool with new features
// inspired by the fetch-mcp reference implementation
func main() {
	fmt.Println("=== Optimized Fetch Webpage Tool Demo ===\n")

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	// Create the optimized webpage fetching tool
	tool := fetch_webpage.New(ctx)

	// Display enhanced tool information
	fmt.Printf("Tool Name: %s\n", tool.Name())
	fmt.Printf("Tool Description: %s\n", tool.Description())
	fmt.Println()

	// Display enhanced parameters
	fmt.Println("Enhanced Tool Parameters:")
	params := tool.Parameters()
	for name, spec := range params {
		fmt.Printf("  - %s (%s): %s [Required: %v]", 
			name, spec.Type, spec.Description, spec.Required)
		if spec.Default != nil {
			fmt.Printf(" [Default: %v]", spec.Default)
		}
		if spec.Enum != nil {
			fmt.Printf(" [Options: %v]", spec.Enum)
		}
		fmt.Println()
	}
	fmt.Println()

	// Demo 1: Basic text extraction with content limiting
	fmt.Println("=== Demo 1: Basic Text Extraction with Content Limiting ===")
	basicParams := fetch_webpage.WebpageParam{
		Url:       "https://example.com",
		MaxLength: 300,
		Format:    "text",
	}
	
	paramsJSON, _ := json.Marshal(basicParams)
	result, err := tool.Execute(ctx, string(paramsJSON))
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Printf("Result (limited to 300 chars):\n%s\n", result)
	}
	fmt.Println()

	// Demo 2: HTML format output
	fmt.Println("=== Demo 2: HTML Format Output ===")
	htmlParams := fetch_webpage.WebpageParam{
		Url:       "https://httpbin.org/html",
		MaxLength: 500,
		Format:    "html",
	}
	
	paramsJSON, _ = json.Marshal(htmlParams)
	result, err = tool.Execute(ctx, string(paramsJSON))
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Printf("HTML Result:\n%s\n", result)
	}
	fmt.Println()

	// Demo 3: Markdown format output
	fmt.Println("=== Demo 3: Markdown Format Output ===")
	markdownParams := fetch_webpage.WebpageParam{
		Url:       "https://httpbin.org/html",
		MaxLength: 800,
		Format:    "markdown",
	}
	
	paramsJSON, _ = json.Marshal(markdownParams)
	result, err = tool.Execute(ctx, string(paramsJSON))
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Printf("Markdown Result:\n%s\n", result)
	}
	fmt.Println()

	// Demo 4: JSON format output
	fmt.Println("=== Demo 4: JSON Format Output ===")
	jsonParams := fetch_webpage.WebpageParam{
		Url:    "https://httpbin.org/json",
		Format: "json",
	}
	
	paramsJSON, _ = json.Marshal(jsonParams)
	result, err = tool.Execute(ctx, string(paramsJSON))
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Printf("JSON Result:\n%s\n", result)
	}
	fmt.Println()

	// Demo 5: Custom headers
	fmt.Println("=== Demo 5: Custom Headers ===")
	headersParams := fetch_webpage.WebpageParam{
		Url: "https://httpbin.org/headers",
		Headers: map[string]string{
			"X-Custom-Header": "OptimizedFetchTool",
			"Accept":          "application/json",
		},
		Format: "text",
	}
	
	paramsJSON, _ = json.Marshal(headersParams)
	result, err = tool.Execute(ctx, string(paramsJSON))
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Printf("Custom Headers Result:\n%s\n", result)
	}
	fmt.Println()

	// Demo 6: Content pagination with start_index
	fmt.Println("=== Demo 6: Content Pagination ===")
	paginationParams1 := fetch_webpage.WebpageParam{
		Url:        "https://example.com",
		StartIndex: 0,
		MaxLength:  200,
		Format:     "text",
	}
	
	paramsJSON, _ = json.Marshal(paginationParams1)
	result, err = tool.Execute(ctx, string(paramsJSON))
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Printf("First 200 characters:\n%s\n", result)
	}

	paginationParams2 := fetch_webpage.WebpageParam{
		Url:        "https://example.com",
		StartIndex: 200,
		MaxLength:  200,
		Format:     "text",
	}
	
	paramsJSON, _ = json.Marshal(paginationParams2)
	result, err = tool.Execute(ctx, string(paramsJSON))
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Printf("Next 200 characters (starting from index 200):\n%s\n", result)
	}
	fmt.Println()

	// Demo 7: Security test - private IP blocking
	fmt.Println("=== Demo 7: Security Test - Private IP Blocking ===")
	securityParams := fetch_webpage.WebpageParam{
		Url:    "http://127.0.0.1:8080",
		Format: "text",
	}
	
	paramsJSON, _ = json.Marshal(securityParams)
	result, err = tool.Execute(ctx, string(paramsJSON))
	if err != nil {
		fmt.Printf("✅ Security working - blocked private IP: %v\n", err)
	} else {
		fmt.Printf("❌ Security issue - should have blocked private IP\n")
	}
	fmt.Println()

	// Demo 8: Error handling for invalid URLs
	fmt.Println("=== Demo 8: Error Handling ===")
	errorParams := fetch_webpage.WebpageParam{
		Url:    "https://nonexistent-domain-12345.com",
		Format: "text",
	}
	
	paramsJSON, _ = json.Marshal(errorParams)
	result, err = tool.Execute(ctx, string(paramsJSON))
	if err != nil {
		fmt.Printf("✅ Error handling working: %v\n", err)
	} else {
		fmt.Printf("Unexpected success: %s\n", result)
	}

	fmt.Println("\n=== Optimization Summary ===")
	fmt.Println("✅ Enhanced security with private IP blocking")
	fmt.Println("✅ Multiple output formats (text, html, markdown, json)")
	fmt.Println("✅ Content length limiting and pagination")
	fmt.Println("✅ Custom HTTP headers support")
	fmt.Println("✅ Improved HTML parsing with golang.org/x/net/html")
	fmt.Println("✅ Better error handling and validation")
	fmt.Println("✅ Structured parameter handling")
	fmt.Println("✅ Backward compatibility maintained")
	
	fmt.Println("\n=== Demo Complete ===")
}
