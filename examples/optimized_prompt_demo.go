package main

import (
	"context"
	"fmt"
	"log"

	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/run-bigpig/hongdou/internal/pkg/multitenancy"
	"github.com/run-bigpig/hongdou/internal/service/mastermind"
	"github.com/run-bigpig/hongdou/internal/service/message"
)

// This example demonstrates the optimized buildEnhancedPrompt function
// that creates comprehensive user prompts with integrated memory and context

func main() {
	fmt.Println("=== Optimized Prompt Construction Demo ===\n")
	
	// Demonstrate the optimized prompt with realistic data
	demonstrateOptimizedPrompt()
}

func demonstrateOptimizedPrompt() {
	// Create user context with proper isolation
	ctx := context.Background()
	ctx = multitenancy.WithOrgID(ctx, "tech-startup-inc")
	ctx = multitenancy.WithUserID(ctx, "sarah-data-scientist")
	
	// Create realistic long-term memory with user-specific context
	longTermMemory := &MockMemory{
		messages: []interfaces.Message{
			{
				Role:    "system",
				Content: "You are an expert data scientist and ML engineer. Always prioritize data quality, model interpretability, and ethical AI practices. Consider scalability and production readiness in your recommendations.",
				Metadata: map[string]interface{}{
					"timestamp":   1703000000,
					"memory_type": "system_instruction",
					"importance":  "critical",
				},
			},
			{
				Role:    "user",
				Content: "I prefer using Python with scikit-learn and pandas for most ML projects because of the ecosystem and documentation",
				Metadata: map[string]interface{}{
					"timestamp":   1703100000,
					"memory_type": "user_preference",
					"topic":       "ml_tools",
				},
			},
			{
				Role:    "assistant",
				Content: "I'll remember your preference for Python, scikit-learn, and pandas. They're excellent choices for ML projects with great community support and extensive documentation.",
				Metadata: map[string]interface{}{
					"timestamp": 1703100060,
				},
			},
			{
				Role:    "user",
				Content: "I work with customer behavior data and need to be very careful about privacy compliance and data anonymization",
				Metadata: map[string]interface{}{
					"timestamp":   1703200000,
					"memory_type": "compliance_requirement",
					"topic":       "privacy",
				},
			},
			{
				Role:    "system",
				Content: "Always ensure GDPR and CCPA compliance when working with customer data. Recommend privacy-preserving techniques like differential privacy, k-anonymity, or federated learning when applicable.",
				Metadata: map[string]interface{}{
					"timestamp":   1703000000,
					"memory_type": "compliance_rule",
					"importance":  "critical",
				},
			},
			{
				Role:    "user",
				Content: "I've had success with ensemble methods like Random Forest and Gradient Boosting for customer segmentation tasks",
				Metadata: map[string]interface{}{
					"timestamp":   1703300000,
					"memory_type": "past_experience",
					"topic":       "ml_algorithms",
				},
			},
		},
	}
	
	// Create recent conversation history
	shortTermMemory := &MockMemory{
		messages: []interfaces.Message{
			{
				Role:    "user",
				Content: "I'm starting a new project to predict customer churn for our SaaS platform",
				Metadata: map[string]interface{}{
					"timestamp": 1703400000,
				},
			},
			{
				Role:    "assistant",
				Content: "That's a valuable project! Customer churn prediction can significantly impact business retention strategies. What type of customer data do you have available?",
				Metadata: map[string]interface{}{
					"timestamp": 1703400060,
				},
			},
			{
				Role:    "user",
				Content: "We have user activity logs, subscription history, support ticket data, and feature usage metrics",
				Metadata: map[string]interface{}{
					"timestamp": 1703400120,
				},
			},
			{
				Role:    "assistant",
				Content: "Excellent! That's rich data for churn prediction. The combination of behavioral, transactional, and support data should provide strong predictive signals.",
				Metadata: map[string]interface{}{
					"timestamp": 1703400180,
				},
			},
		},
	}
	
	// Create message services
	shortMemoryMessage := message.NewMessage(ctx, shortTermMemory)
	longMemoryMessage := message.NewMessage(ctx, longTermMemory)
	
	// Create mastermind instance
	mind := &mastermind.Mastermind{
		// Note: In real usage, these would be injected via constructor
	}
	
	// Current user query
	userQuery := "What's the best approach for building a churn prediction model? I want to ensure high accuracy while maintaining model interpretability for business stakeholders."
	
	// Available tools context
	toolsContext := `Available Tools & Capabilities:
- data_profiler: Analyze dataset characteristics, missing values, and data quality
- feature_engineer: Generate and select relevant features for ML models
- model_trainer: Train various ML algorithms with hyperparameter optimization
- model_evaluator: Evaluate model performance with comprehensive metrics
- explainability_analyzer: Generate model explanations and feature importance
- privacy_auditor: Check data privacy compliance and suggest anonymization
- deployment_planner: Plan model deployment and monitoring strategies
- business_impact_calculator: Estimate business impact and ROI of ML models`
	
	// Build the optimized prompt
	fmt.Println("Building optimized user prompt with:")
	fmt.Printf("- User Context: %s\n", "tech-startup-inc:sarah-data-scientist")
	fmt.Printf("- Long-term memories: %d messages\n", len(longTermMemory.messages))
	fmt.Printf("- Recent conversation: %d messages\n", len(shortTermMemory.messages))
	fmt.Printf("- User Query: %s\n", userQuery)
	fmt.Println()
	
	// Simulate the optimized prompt generation
	optimizedPrompt := buildOptimizedPromptDemo(userQuery, toolsContext, shortTermMemory, longTermMemory)
	
	fmt.Println("=== GENERATED OPTIMIZED USER PROMPT ===")
	fmt.Println(optimizedPrompt)
	
	fmt.Println("\n=== KEY OPTIMIZATIONS DEMONSTRATED ===")
	fmt.Println("✅ User-specific context isolation (tech-startup-inc:sarah-data-scientist)")
	fmt.Println("✅ Proper error handling for GetOrgID/GetUserID")
	fmt.Println("✅ Enhanced memory retrieval with semantic relevance")
	fmt.Println("✅ Structured information hierarchy for optimal LLM comprehension")
	fmt.Println("✅ System instructions separated from personal preferences")
	fmt.Println("✅ Recent conversation context for continuity")
	fmt.Println("✅ Clear current query highlighting")
	fmt.Println("✅ Comprehensive analysis framework")
	fmt.Println("✅ Structured JSON response format specification")
	fmt.Println("✅ Privacy and compliance awareness")
	fmt.Println("✅ Concise yet comprehensive context integration")
}

func buildOptimizedPromptDemo(query, toolsContext string, shortMem, longMem *MockMemory) string {
	return fmt.Sprintf(`## USER CONTEXT
**User**: sarah-data-scientist (Organization: tech-startup-inc)

## RELEVANT CONTEXT FROM MEMORY

### System Instructions & Guidelines
1. You are an expert data scientist and ML engineer. Always prioritize data quality, model interpretability, and ethical AI practices. Consider scalability and production readiness in your recommendations.
2. Always ensure GDPR and CCPA compliance when working with customer data. Recommend privacy-preserving techniques like differential privacy, k-anonymity, or federated learning when applicable.

### Personal Context & Preferences
1. [user] I prefer using Python with scikit-learn and pandas for most ML projects because of the ecosystem and documentation
2. [assistant] I'll remember your preference for Python, scikit-learn, and pandas. They're excellent choices for ML projects with great community support and extensive documentation.
3. [user] I work with customer behavior data and need to be very careful about privacy compliance and data anonymization
4. [user] I've had success with ensemble methods like Random Forest and Gradient Boosting for customer segmentation tasks

*Note: Above context retrieved based on semantic relevance to your query.*

## RECENT CONVERSATION HISTORY
` + "```" + `
user: I'm starting a new project to predict customer churn for our SaaS platform
assistant: That's a valuable project! Customer churn prediction can significantly impact business retention strategies. What type of customer data do you have available?
user: We have user activity logs, subscription history, support ticket data, and feature usage metrics
assistant: Excellent! That's rich data for churn prediction. The combination of behavioral, transactional, and support data should provide strong predictive signals.
` + "```" + `

## AVAILABLE TOOLS & CAPABILITIES
%s

## CURRENT USER QUERY
**Query**: %s

## ANALYSIS & RESPONSE INSTRUCTIONS

Please analyze the above context and user query following this structured approach:

### 1. Context Analysis
- Review the user's personal context, preferences, and history
- Consider any system instructions or guidelines
- Identify patterns from previous interactions

### 2. Intent & Complexity Assessment
- Determine the primary intent behind the user's query
- Assess the complexity level (simple vs. complex task)
- Identify required capabilities and potential tools needed

### 3. Dispatch Decision
Choose the appropriate response mode:

**DIRECT_ANSWER** - Use when:
- Query can be answered directly with available context
- No external tools or complex processing required
- Answer can be provided immediately

**AGENT_EXECUTION** - Use when:
- Query requires tool usage or external data access
- Complex multi-step processing needed
- Specialized agent capabilities required

### 4. Response Format
Provide your response as a JSON object with this structure:

` + "```json" + `
{
  "dispatch_mode": "DIRECT_ANSWER" | "AGENT_EXECUTION",
  "reasoning": "Your step-by-step analysis explaining the decision",
  "answer": "Direct answer (only if dispatch_mode is DIRECT_ANSWER)",
  "sub_agents": [
    {
      "agent_name": "descriptive_name",
      "sub_question": "specific_question_for_agent",
      "system_prompt": "detailed_instructions_for_agent",
      "enabled_tools": ["tool1", "tool2"]
    }
  ],
  "final_synthesis_prompt": "Instructions for combining agent results (if multiple agents)"
}
` + "```" + `

**Important**: Ensure your response is valid JSON and includes all required fields based on the chosen dispatch_mode.`, toolsContext, query)
}

// MockMemory for demonstration
type MockMemory struct {
	messages []interfaces.Message
}

func (m *MockMemory) AddMessage(ctx context.Context, message interfaces.Message) error {
	m.messages = append(m.messages, message)
	return nil
}

func (m *MockMemory) GetMessages(ctx context.Context, options ...interfaces.GetMessagesOption) ([]interfaces.Message, error) {
	return m.messages, nil
}

func (m *MockMemory) Clear(ctx context.Context) error {
	m.messages = nil
	return nil
}
