package main

import (
	"context"
	"fmt"
	"log"

	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/run-bigpig/hongdou/internal/pkg/multitenancy"
	"github.com/run-bigpig/hongdou/internal/service/mastermind"
	"github.com/run-bigpig/hongdou/internal/service/message"
)

// This example demonstrates the enhanced prompt construction system
// that leverages user-specific memory isolation and advanced prompt engineering

func main() {
	fmt.Println("=== Enhanced Prompt Construction Example ===\n")
	
	// Simulate a realistic scenario with user context and memory
	demonstrateEnhancedPromptConstruction()
}

func demonstrateEnhancedPromptConstruction() {
	// Create user context
	ctx := context.Background()
	ctx = multitenancy.WithOrgID(ctx, "acme-corp")
	ctx = multitenancy.WithUserID(ctx, "alice-developer")
	
	// Create mock memories with realistic data
	shortTermMemory := &MockMemory{
		messages: []interfaces.Message{
			{
				Role:    "user",
				Content: "I'm working on a machine learning project for customer segmentation",
				Metadata: map[string]interface{}{
					"timestamp": 1703001600, // Recent timestamp
					"topic":     "ml_project",
				},
			},
			{
				Role:    "assistant", 
				Content: "That sounds interesting! Customer segmentation is a valuable ML application. What type of data are you working with?",
				Metadata: map[string]interface{}{
					"timestamp": 1703001660,
				},
			},
			{
				Role:    "user",
				Content: "I have customer transaction data with features like purchase amount, frequency, and product categories",
				Metadata: map[string]interface{}{
					"timestamp": 1703001720,
					"topic":     "data_features",
				},
			},
			{
				Role:    "assistant",
				Content: "Perfect! That's rich data for segmentation. Are you considering clustering algorithms like K-means or hierarchical clustering?",
				Metadata: map[string]interface{}{
					"timestamp": 1703001780,
				},
			},
		},
	}
	
	longTermMemory := &MockMemory{
		messages: []interfaces.Message{
			{
				Role:    "system",
				Content: "You are an expert data scientist specializing in machine learning and customer analytics. Always prioritize data quality and interpretability in your recommendations.",
				Metadata: map[string]interface{}{
					"timestamp":   1702000000,
					"memory_type": "system_instruction",
					"importance":  "high",
				},
			},
			{
				Role:    "user",
				Content: "I prefer using scikit-learn for machine learning projects because of its simplicity and documentation",
				Metadata: map[string]interface{}{
					"timestamp":   1702500000,
					"memory_type": "user_preference",
					"topic":       "ml_tools",
				},
			},
			{
				Role:    "assistant",
				Content: "I'll remember your preference for scikit-learn. It's an excellent choice for most ML tasks with great documentation and community support.",
				Metadata: map[string]interface{}{
					"timestamp": 1702500060,
				},
			},
			{
				Role:    "user",
				Content: "I work with retail e-commerce data and need to be careful about customer privacy",
				Metadata: map[string]interface{}{
					"timestamp":   1702600000,
					"memory_type": "user_context",
					"topic":       "privacy_requirements",
				},
			},
			{
				Role:    "system",
				Content: "Always ensure GDPR compliance and data anonymization when working with customer data. Recommend privacy-preserving techniques when applicable.",
				Metadata: map[string]interface{}{
					"timestamp":   1702000000,
					"memory_type": "compliance_rule",
					"importance":  "critical",
				},
			},
			{
				Role:    "user",
				Content: "I've had good results with K-means clustering in previous projects",
				Metadata: map[string]interface{}{
					"timestamp":   1702700000,
					"memory_type": "past_experience",
					"topic":       "clustering_algorithms",
				},
			},
		},
	}
	
	// Create message services
	shortMemoryMessage := message.NewMessage(ctx, shortTermMemory)
	longMemoryMessage := message.NewMessage(ctx, longTermMemory)
	
	// Create mastermind instance
	mind := &mastermind.Mastermind{
		// Note: In real usage, these would be injected via constructor
		// This is simplified for demonstration
	}
	
	// Simulate setting the context and memory services
	// (In real implementation, this would be done through proper dependency injection)
	
	// Current user query
	userQuery := "What's the best approach for customer segmentation with my transaction data? I want to identify high-value customer groups."
	
	// Available tools context
	toolsContext := `Available Tools:
- data_analyzer: Analyze dataset characteristics and quality
- ml_recommender: Recommend appropriate ML algorithms based on data
- clustering_evaluator: Evaluate clustering results and suggest optimal parameters
- privacy_checker: Ensure data privacy compliance
- visualization_generator: Create charts and plots for data exploration
- performance_optimizer: Optimize model performance and scalability`
	
	// Build the enhanced prompt
	fmt.Println("Building enhanced prompt with:")
	fmt.Printf("- User Context: %s\n", "acme-corp:alice-developer")
	fmt.Printf("- Short-term memories: %d messages\n", len(shortTermMemory.messages))
	fmt.Printf("- Long-term memories: %d messages\n", len(longTermMemory.messages))
	fmt.Printf("- User Query: %s\n", userQuery)
	fmt.Println()
	
	// This would normally call the actual buildEnhancedPrompt method
	// For demonstration, we'll show what the enhanced prompt would look like
	enhancedPrompt := buildDemoPrompt(userQuery, toolsContext, shortTermMemory, longTermMemory)
	
	fmt.Println("=== GENERATED ENHANCED PROMPT ===")
	fmt.Println(enhancedPrompt)
	
	fmt.Println("\n=== KEY IMPROVEMENTS DEMONSTRATED ===")
	fmt.Println("✅ Clear role definition with specific capabilities")
	fmt.Println("✅ User-specific context isolation (acme-corp:alice-developer)")
	fmt.Println("✅ System instructions with compliance requirements")
	fmt.Println("✅ Personal preferences and past experiences")
	fmt.Println("✅ Recent conversation context")
	fmt.Println("✅ Structured analysis framework with chain-of-thought")
	fmt.Println("✅ Clear dispatch mode selection criteria")
	fmt.Println("✅ Detailed JSON output format specification")
	fmt.Println("✅ Privacy and compliance awareness")
	fmt.Println("✅ Tool-aware decision making")
}

func buildDemoPrompt(query, toolsContext string, shortMem, longMem *MockMemory) string {
	return fmt.Sprintf(`# ROLE: HongDou Mind - Intelligent Task Analysis & Dispatch System

You are HongDou Mind, an advanced AI system responsible for analyzing user queries and determining the optimal execution strategy. Your core capabilities include:
- Deep analysis of user intent and context
- Strategic decision-making for task dispatch
- Intelligent agent orchestration and planning
- Context-aware response generation

## CONTEXT INFORMATION

**User Context**: acme-corp:alice-developer

### System Context & Instructions
1. You are an expert data scientist specializing in machine learning and customer analytics. Always prioritize data quality and interpretability in your recommendations.
2. Always ensure GDPR compliance and data anonymization when working with customer data. Recommend privacy-preserving techniques when applicable.

### Personal Context & History
1. [user] I prefer using scikit-learn for machine learning projects because of its simplicity and documentation
2. [assistant] I'll remember your preference for scikit-learn. It's an excellent choice for most ML tasks with great documentation and community support.
3. [user] I work with retail e-commerce data and need to be careful about customer privacy
4. [user] I've had good results with K-means clustering in previous projects

### Recent Conversation History
` + "```" + `
user: I'm working on a machine learning project for customer segmentation
assistant: That sounds interesting! Customer segmentation is a valuable ML application. What type of data are you working with?
user: I have customer transaction data with features like purchase amount, frequency, and product categories
assistant: Perfect! That's rich data for segmentation. Are you considering clustering algorithms like K-means or hierarchical clustering?
user: %s
` + "```" + `

## AVAILABLE TOOLS & CAPABILITIES

%s

## CURRENT USER QUERY

**Query**: %s

## ANALYSIS & DECISION FRAMEWORK

Please analyze the user query using the following chain-of-thought approach:

### Step 1: Context Analysis
- Review the user's personal context and history
- Identify relevant patterns from previous interactions
- Consider system instructions and constraints

### Step 2: Intent Classification
- Determine the primary intent behind the query
- Assess complexity level (simple vs. complex)
- Identify required capabilities and tools

### Step 3: Dispatch Decision
Choose the optimal dispatch mode:

**DIRECT_ANSWER**: Use when:
- Query is straightforward and can be answered directly
- No external tools or complex processing needed
- Answer can be derived from available context

**AGENT_EXECUTION**: Use when:
- Query requires tool usage or external data
- Complex multi-step processing needed
- Specialized agent capabilities required

### Step 4: Execution Planning
If AGENT_EXECUTION is chosen:
- Design optimal agent configuration
- Select appropriate tools for each agent
- Create clear sub-questions and system prompts
- Plan result synthesis strategy

## REQUIRED OUTPUT FORMAT

Respond with a valid JSON object following this exact structure:

` + "```json" + `
{
  "dispatch_mode": "DIRECT_ANSWER" | "AGENT_EXECUTION",
  "reasoning": "Your step-by-step analysis and decision rationale",
  "answer": "Direct answer (only if dispatch_mode is DIRECT_ANSWER)",
  "sub_agents": [
    {
      "agent_name": "descriptive_agent_name",
      "sub_question": "specific_question_for_agent",
      "system_prompt": "detailed_system_prompt",
      "enabled_tools": ["tool1", "tool2"]
    }
  ],
  "final_synthesis_prompt": "Instructions for combining agent results"
}
` + "```" + `

**IMPORTANT**: Ensure your response is valid JSON and includes all required fields based on the chosen dispatch_mode.`, query, toolsContext, query)
}

// MockMemory for demonstration
type MockMemory struct {
	messages []interfaces.Message
}

func (m *MockMemory) AddMessage(ctx context.Context, message interfaces.Message) error {
	m.messages = append(m.messages, message)
	return nil
}

func (m *MockMemory) GetMessages(ctx context.Context, options ...interfaces.GetMessagesOption) ([]interfaces.Message, error) {
	return m.messages, nil
}

func (m *MockMemory) Clear(ctx context.Context) error {
	m.messages = nil
	return nil
}
