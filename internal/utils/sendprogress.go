package utils

import "github.com/run-bigpig/hongdou/internal/types"

// SendProgress sends a progress update if streaming is enabled
func SendProgress(progressChan chan<- types.StreamResponse, message string) {
	if progressChan != nil {
		select {
		case progressChan <- types.StreamResponse{
			Type:    "progress",
			Content: message,
			Done:    false,
		}:
		default:
			// Channel is full, skip this update
		}
	}
}
