# Fetch Webpage Tool

## Overview

The `fetch_webpage` tool is a web content fetching utility that retrieves and parses webpage information from any given URL. It follows the same architectural patterns and conventions as other tools in the HongDou system, particularly mirroring the implementation structure of the `get_weather` tool.

## Features

- **URL Validation**: Automatically validates and cleans input URLs
- **Protocol Handling**: Automatically adds HTTPS protocol if missing
- **Content Extraction**: Extracts title, meta description, and content preview
- **Error Handling**: Comprehensive error handling for network issues, invalid URLs, and HTTP errors
- **Content Type Detection**: Handles both HTML and non-HTML content appropriately
- **Text Cleaning**: Removes HTML tags and entities, normalizes whitespace
- **Redirect Support**: Follows up to 10 redirects automatically
- **Timeout Protection**: 30-second timeout to prevent hanging requests
- **Logging**: Integrated with the project's logging system

## Usage

### Basic Usage

```go
import (
    "context"
    "github.com/run-bigpig/hongdou/internal/service/tools/fetch_webpage"
)

// Create the tool
ctx := context.Background()
tool := fetch_webpage.New(ctx)

// Fetch webpage content
result, err := tool.Run(ctx, "https://example.com")
if err != nil {
    log.Printf("Error: %v", err)
} else {
    fmt.Println(result)
}
```

### Tool Interface Methods

The tool implements the `interfaces.Tool` interface with the following methods:

- `Name() string` - Returns "fetch_webpage"
- `Description() string` - Returns tool description in Chinese
- `Run(ctx context.Context, input string) (string, error)` - Main execution method
- `Execute(ctx context.Context, args string) (string, error)` - Alias for Run method
- `Parameters() map[string]interfaces.ParameterSpec` - Returns parameter specifications

### Parameters

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| url       | string | Yes      | The webpage URL to fetch content from |

### Response Format

The tool returns a formatted string containing:

```
URL: <cleaned_url>
标题: <page_title>
描述: <meta_description>
状态码: <http_status_code>
内容预览: <content_preview>
```

### URL Input Formats

The tool accepts various URL formats:

- `https://example.com` - Full URL with protocol
- `http://example.com` - HTTP protocol
- `example.com` - Automatically adds HTTPS
- `www.example.com` - Automatically adds HTTPS
- `example.com/path?query=value` - URLs with paths and queries

## Implementation Details

### Architecture

The tool follows the established patterns in the HongDou codebase:

1. **Package Structure**: Located in `internal/service/tools/fetch_webpage/`
2. **Constructor Pattern**: Uses `New(ctx context.Context)` constructor
3. **Interface Implementation**: Implements `interfaces.Tool`
4. **Logging Integration**: Uses `logx.Logger` from go-zero
5. **Context Handling**: Proper context propagation for cancellation

### HTTP Client Configuration

- **Timeout**: 30 seconds
- **Redirects**: Up to 10 redirects allowed
- **User Agent**: "HongDou-WebpageFetcher/1.0"
- **Accept Headers**: Optimized for HTML content

### Content Processing

1. **HTML Parsing**: Uses regex patterns to extract:
   - `<title>` tags for page titles
   - `<meta name="description">` for descriptions
   - `<meta property="og:description">` as fallback
   
2. **Text Cleaning**:
   - Removes HTML tags and scripts
   - Converts HTML entities (&nbsp;, &amp;, etc.)
   - Normalizes whitespace
   - Truncates content for preview

3. **Error Handling**:
   - Network timeouts
   - Invalid URLs
   - HTTP error status codes
   - Non-HTML content types

## Testing

The tool includes comprehensive tests covering:

- Unit tests for all public methods
- URL validation and cleaning
- HTML content extraction
- Text processing functions
- Integration tests with real HTTP requests

Run tests with:
```bash
go test -v ./internal/service/tools/fetch_webpage
```

For integration tests only:
```bash
go test -v ./internal/service/tools/fetch_webpage -run Integration
```

## Integration

The tool is automatically registered in the tools system through:

1. **Import**: Added to `internal/service/tools/tools.go`
2. **Registration**: Added to `loadSelfTools()` method
3. **Discovery**: Available through the tool discovery system

## Error Handling

Common error scenarios and their handling:

| Error Type | Handling |
|------------|----------|
| Invalid URL | Returns validation error with details |
| Network timeout | Returns timeout error after 30 seconds |
| HTTP errors | Returns error with status code |
| Non-HTML content | Returns content type information |
| Connection refused | Returns connection error |

## Security Considerations

- **URL Validation**: Prevents malformed URLs
- **Timeout Protection**: Prevents hanging requests
- **Redirect Limits**: Prevents redirect loops
- **Content Size**: Limits content preview size
- **User Agent**: Identifies requests appropriately

## Performance

- **Concurrent Safe**: Can handle multiple concurrent requests
- **Memory Efficient**: Limits content processing size
- **Network Optimized**: Proper timeout and redirect handling
- **Caching**: No built-in caching (can be added if needed)

## Future Enhancements

Potential improvements that could be added:

1. **Content Caching**: Cache responses for repeated URLs
2. **Content Type Support**: Better handling of PDF, images, etc.
3. **Structured Data**: Extract JSON-LD, microdata
4. **Language Detection**: Detect webpage language
5. **Screenshot Capture**: Generate webpage screenshots
6. **Content Summarization**: AI-powered content summarization

## Dependencies

- Standard library: `net/http`, `net/url`, `regexp`, `strings`, `time`
- Project dependencies: `interfaces`, `logx` (go-zero logging)

## Compatibility

- Go version: 1.24+
- Compatible with existing tool system architecture
- Follows project coding standards and conventions
