package fetch_webpage

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.org/x/net/html"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/zeromicro/go-zero/core/logx"
)

// WebpageTool 实现了 Tool 接口，提供网页内容获取功能
type WebpageTool struct {
	ctx    context.Context
	logger logx.Logger
	client *http.Client
}

type WebpageParam struct {
	Url string `json:"url"`
}

// 定义一些评分常量，方便调整
const (
	paragraphBoost     = 25.0 // 为每个 <p> 标签增加的分数
	textLengthBoost    = 1.0  // 每100个字符增加的分数
	commaBoost         = 5.0  // 为每个逗号增加的分数
	linkDensityPenalty = 0.7  // 链接密度惩罚系数，值越小惩罚越重
	scoreDecayFactor   = 0.75 // 子节点分数传递给父节点时的衰减系数
)

// 定义需要直接移除的标签
var unlikelyTags = map[string]bool{
	"script":   true,
	"style":    true,
	"noscript": true,
	"iframe":   true,
	"nav":      true,
	"aside":    true,
	"footer":   true,
	"header":   true,
	"form":     true,
	"button":   true,
	"select":   true,
	"textarea": true,
}

// 定义需要降低分数的 class/id 正则表达式
var unlikelyPatterns = regexp.MustCompile(`(?i)comment|sidebar|community|ad-break|advert|social|pagination|share|extra|header|menu|nav|remark|footer|footnote`)

// candidateNode 用于存储候选节点及其分数信息
type candidateNode struct {
	node           *html.Node
	score          float64
	textLength     int
	linkTextLength int
}

func New(ctx context.Context) *WebpageTool {
	return &WebpageTool{
		ctx:    ctx,
		logger: logx.WithContext(ctx),
		client: &http.Client{
			Timeout: 30 * time.Second,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				// Allow up to 10 redirects
				if len(via) >= 10 {
					return fmt.Errorf("too many redirects")
				}
				return nil
			},
		},
	}
}

// Name 返回工具名称
func (w *WebpageTool) Name() string {
	return "fetch_webpage"
}

// Description 返回工具描述
func (w *WebpageTool) Description() string {
	return "获取指定URL的网页内容信息，包括标题、描述和主要内容"
}

// Run 执行工具并返回网页信息
func (w *WebpageTool) Run(ctx context.Context, url string) (string, error) {
	webpageData, err := w.fetchWebpageData(ctx, url)
	if err != nil {
		w.logger.Errorf("获取网页信息失败: %v", err)
		return "", err
	}

	return fmt.Sprintf("URL: %s\n标题: %s\n描述: %s\n状态码: %d\n内容: %s",
		webpageData.URL, webpageData.Title, webpageData.Description,
		webpageData.StatusCode, webpageData.Content), nil
}

// Execute 与 Run 相同，但接受参数字符串
func (w *WebpageTool) Execute(ctx context.Context, args string) (string, error) { //解析参数
	var params WebpageParam
	if err := json.Unmarshal([]byte(args), &params); err != nil {
		w.logger.Errorf("解析参数失败: %v", err)
		return "", err
	}

	return w.Run(ctx, params.Url)
}

// Parameters 返回工具接受的参数规范
func (w *WebpageTool) Parameters() map[string]interfaces.ParameterSpec {
	return map[string]interfaces.ParameterSpec{
		"url": {
			Type:        "string",
			Description: "要获取内容的网页URL地址",
			Required:    true,
		},
	}
}

type webpageResponse struct {
	URL         string `json:"url"`
	Title       string `json:"title"`
	Description string `json:"description"`
	StatusCode  int    `json:"status_code"`
	Content     string `json:"content_preview"`
	ContentType string `json:"content_type"`
}

// fetchWebpageData 从指定URL获取网页数据
func (w *WebpageTool) fetchWebpageData(ctx context.Context, inputURL string) (*webpageResponse, error) {
	// 验证和清理URL
	cleanURL, err := w.validateAndCleanURL(inputURL)
	if err != nil {
		return nil, fmt.Errorf("无效的URL: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", cleanURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")

	// 发送请求
	resp, err := w.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	// 检查内容类型
	contentType := resp.Header.Get("Content-Type")
	if !strings.Contains(strings.ToLower(contentType), "text/html") {
		return &webpageResponse{
			URL:         cleanURL,
			Title:       "非HTML内容",
			Description: fmt.Sprintf("内容类型: %s", contentType),
			StatusCode:  resp.StatusCode,
			Content:     w.truncateText(string(body), 200),
			ContentType: contentType,
		}, nil
	}

	// 解析HTML内容
	htmlContent := string(body)
	title := w.extractTitle(htmlContent)
	description := w.extractDescription(htmlContent)
	content := w.extractContent(htmlContent)

	return &webpageResponse{
		URL:         cleanURL,
		Title:       title,
		Description: description,
		StatusCode:  resp.StatusCode,
		Content:     content,
		ContentType: contentType,
	}, nil
}

// validateAndCleanURL 验证和清理URL
func (w *WebpageTool) validateAndCleanURL(inputURL string) (string, error) {
	// 去除前后空格
	inputURL = strings.TrimSpace(inputURL)

	// 如果没有协议，添加https://
	if !strings.HasPrefix(inputURL, "http://") && !strings.HasPrefix(inputURL, "https://") {
		inputURL = "https://" + inputURL
	}

	// 验证URL格式
	parsedURL, err := url.Parse(inputURL)
	if err != nil {
		return "", err
	}

	// 检查是否有主机名
	if parsedURL.Host == "" {
		return "", fmt.Errorf("URL缺少主机名")
	}

	return parsedURL.String(), nil
}

// extractTitle 从HTML中提取标题
func (w *WebpageTool) extractTitle(html string) string {
	titleRegex := regexp.MustCompile(`(?i)<title[^>]*>([^<]*)</title>`)
	matches := titleRegex.FindStringSubmatch(html)
	if len(matches) > 1 {
		return w.cleanText(matches[1])
	}
	return "未找到标题"
}

// extractDescription 从HTML中提取描述
func (w *WebpageTool) extractDescription(html string) string {
	// 尝试提取meta description
	descRegex := regexp.MustCompile(`(?i)<meta[^>]*name=["']description["'][^>]*content=["']([^"']*)["'][^>]*>`)
	matches := descRegex.FindStringSubmatch(html)
	if len(matches) > 1 {
		return w.cleanText(matches[1])
	}

	// 尝试提取og:description
	ogDescRegex := regexp.MustCompile(`(?i)<meta[^>]*property=["']og:description["'][^>]*content=["']([^"']*)["'][^>]*>`)
	matches = ogDescRegex.FindStringSubmatch(html)
	if len(matches) > 1 {
		return w.cleanText(matches[1])
	}

	return "未找到描述"
}

// 从HTML中提取内容预览
func (w *WebpageTool) extractContent(htmlContent string) string {
	doc, err := html.Parse(strings.NewReader(htmlContent))
	if err != nil {
		w.logger.Errorf("解析HTML失败: %v", err)
		return ""
	}

	// 1. 预处理：移除不可能是内容的节点
	preprocessTree(doc)

	// 2. 找到所有候选节点并为它们打分
	candidates := make(map[*html.Node]*candidateNode)
	w.findAndScoreCandidates(doc, candidates)

	// 3. 找到得分最高的候选节点
	bestCandidate := w.findBestCandidate(candidates)
	if bestCandidate == nil {
		// 如果找不到，作为备选方案，提取整个body的文本
		return w.extractTextFromNode(doc, true)
	}
	// 4. 从最佳节点中提取并清理文本
	return w.extractTextFromNode(bestCandidate.node, true)
}

// preprocessTree 遍历DOM树，移除不太可能是内容的节点
func preprocessTree(n *html.Node) {
	// 使用倒序遍历子节点，这样删除时不会影响索引
	for c := n.LastChild; c != nil; c = c.PrevSibling {
		// 递归处理子节点
		preprocessTree(c)

		// 如果子节点是需要移除的标签，则从父节点中断开链接
		if c.Type == html.ElementNode && unlikelyTags[c.Data] {
			n.RemoveChild(c)
		}
	}
}

// findAndScoreCandidates 遍历节点，为潜在的内容容器打分
func (w *WebpageTool) findAndScoreCandidates(n *html.Node, candidates map[*html.Node]*candidateNode) *candidateNode {
	if n.Type != html.ElementNode {
		return nil
	}

	// 初始化当前节点的候选信息
	current := &candidateNode{node: n}

	// 递归处理子节点，并累加它们的分数
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		childCandidate := w.findAndScoreCandidates(c, candidates)
		if childCandidate != nil {
			current.textLength += childCandidate.textLength
			current.linkTextLength += childCandidate.linkTextLength
			current.score += childCandidate.score * scoreDecayFactor
		}
	}

	// 只为块级元素（div, p, article, section等）计算自身分数
	if n.Data == "div" || n.Data == "p" || n.Data == "article" || n.Data == "section" || n.Data == "td" {
		// 计算自身包含的文本信息
		ownText := ""
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			if c.Type == html.TextNode {
				ownText += c.Data
			}
		}
		ownText = strings.TrimSpace(ownText)
		ownTextLength := len([]rune(ownText))

		if ownTextLength > 20 { // 只有文本长度超过阈值才算分
			current.textLength += ownTextLength

			// 1. 基础分：来自文本长度和段落标签
			current.score += float64(ownTextLength) / 100 * textLengthBoost
			if n.Data == "p" {
				current.score += paragraphBoost
			}

			// 2. 加分项：逗号越多，越像正文
			current.score += float64(strings.Count(ownText, ",")) * commaBoost
			current.score += float64(strings.Count(ownText, "，")) * commaBoost

			// 将当前节点加入候选列表
			candidates[n] = current
		}
	}

	return current
}

// findBestCandidate 从所有候选者中选出最优的一个
func (w *WebpageTool) findBestCandidate(candidates map[*html.Node]*candidateNode) *candidateNode {
	var bestCandidate *candidateNode
	maxScore := 0.0

	for _, candidate := range candidates {
		// 计算链接密度惩罚
		linkDensity := float64(candidate.linkTextLength) / float64(candidate.textLength+1)
		score := candidate.score * (1 - linkDensity*linkDensityPenalty)

		// 根据 class/id 再次惩罚
		for _, attr := range candidate.node.Attr {
			if attr.Key == "class" || attr.Key == "id" {
				if unlikelyPatterns.MatchString(attr.Val) {
					score *= 0.25 // 大幅降低分数
				}
			}
		}

		if score > maxScore {
			maxScore = score
			bestCandidate = candidate
		}
	}
	return bestCandidate
}

// extractTextFromNode 从指定节点递归提取纯文本
func (w *WebpageTool) extractTextFromNode(n *html.Node, cleanNewlines bool) string {
	var builder strings.Builder
	var f func(*html.Node)

	f = func(n *html.Node) {
		if n.Type == html.TextNode {
			builder.WriteString(n.Data)
		} else if n.Type == html.ElementNode && (n.Data == "p" || n.Data == "br" || n.Data == "div") {
			// 在块级元素后添加换行，以保留段落感
			builder.WriteString("\n")
		}

		for c := n.FirstChild; c != nil; c = c.NextSibling {
			f(c)
		}
	}

	f(n)

	if !cleanNewlines {
		return strings.Join(strings.Fields(builder.String()), " ")
	}

	// 清理文本：合并多个空白为一个空格，但保留段落间的换行
	lines := strings.Split(builder.String(), "\n")
	var cleanedLines []string
	for _, line := range lines {
		trimmedLine := strings.TrimSpace(line)
		if len(trimmedLine) > 0 {
			// 将行内的多个空白符替换为单个空格
			cleanedLines = append(cleanedLines, strings.Join(strings.Fields(trimmedLine), " "))
		}
	}
	return strings.Join(cleanedLines, "\n\n") // 段落间用双换行分隔
}

// cleanText 清理文本内容
func (w *WebpageTool) cleanText(text string) string {
	// 替换HTML实体
	text = strings.ReplaceAll(text, "&nbsp;", " ")
	text = strings.ReplaceAll(text, "&amp;", "&")
	text = strings.ReplaceAll(text, "&lt;", "<")
	text = strings.ReplaceAll(text, "&gt;", ">")
	text = strings.ReplaceAll(text, "&quot;", "\"")
	text = strings.ReplaceAll(text, "&#39;", "'")

	// 移除多余的空白字符
	spaceRegex := regexp.MustCompile(`\s+`)
	text = spaceRegex.ReplaceAllString(text, " ")

	return strings.TrimSpace(text)
}

// truncateText 截取文本到指定长度
func (w *WebpageTool) truncateText(text string, maxLength int) string {
	if len(text) <= maxLength {
		return text
	}

	// 尝试在单词边界截断
	truncated := text[:maxLength]
	lastSpace := strings.LastIndex(truncated, " ")
	if lastSpace > maxLength/2 {
		truncated = truncated[:lastSpace]
	}

	return truncated + "..."
}
