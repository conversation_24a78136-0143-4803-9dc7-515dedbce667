package fetch_webpage

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.org/x/net/html"
	"io"
	"net"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/zeromicro/go-zero/core/logx"
)

// WebpageTool 实现了 Tool 接口，提供网页内容获取功能
type WebpageTool struct {
	ctx    context.Context
	logger logx.Logger
	client *http.Client
}

type WebpageParam struct {
	Url        string            `json:"url"`
	Headers    map[string]string `json:"headers,omitempty"`
	MaxLength  int               `json:"max_length,omitempty"`
	StartIndex int               `json:"start_index,omitempty"`
	Format     string            `json:"format,omitempty"` // html, text, markdown, json
}

// 定义需要移除的标签
var unwantedTags = map[string]bool{
	"script":   true,
	"style":    true,
	"noscript": true,
	"iframe":   true,
	"nav":      true,
	"aside":    true,
	"footer":   true,
	"header":   true,
	"form":     true,
	"button":   true,
	"select":   true,
	"textarea": true,
	"meta":     true,
	"link":     true,
	"title":    true,
}

func New(ctx context.Context) *WebpageTool {
	return &WebpageTool{
		ctx:    ctx,
		logger: logx.WithContext(ctx),
		client: &http.Client{
			Timeout: 30 * time.Second,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				// Allow up to 10 redirects
				if len(via) >= 10 {
					return fmt.Errorf("too many redirects")
				}
				return nil
			},
		},
	}
}

// Name 返回工具名称
func (w *WebpageTool) Name() string {
	return "fetch_webpage"
}

// Description 返回工具描述
func (w *WebpageTool) Description() string {
	return "获取指定URL的网页内容信息，包括标题、描述和主要内容"
}

// Run 执行工具并返回网页信息
func (w *WebpageTool) Run(ctx context.Context, url string) (string, error) {
	webpageData, err := w.fetchWebpageData(ctx, url)
	if err != nil {
		w.logger.Errorf("获取网页信息失败: %v", err)
		return "", err
	}

	return fmt.Sprintf("URL: %s\n标题: %s\n描述: %s\n状态码: %d\n内容: %s",
		webpageData.URL, webpageData.Title, webpageData.Description,
		webpageData.StatusCode, webpageData.Content), nil
}

// Execute 与 Run 相同，但接受参数字符串
func (w *WebpageTool) Execute(ctx context.Context, args string) (string, error) {
	var params WebpageParam
	if err := json.Unmarshal([]byte(args), &params); err != nil {
		w.logger.Errorf("解析参数失败: %v", err)
		return "", err
	}

	// 设置默认值
	if params.MaxLength == 0 {
		params.MaxLength = 5000
	}
	if params.Format == "" {
		params.Format = "text"
	}

	return w.RunWithParams(ctx, params)
}

// RunWithParams 使用完整参数执行工具
func (w *WebpageTool) RunWithParams(ctx context.Context, params WebpageParam) (string, error) {
	webpageData, err := w.fetchWebpageDataWithParams(ctx, params)
	if err != nil {
		w.logger.Errorf("获取网页信息失败: %v", err)
		return "", err
	}

	// 根据格式返回不同的结果
	switch params.Format {
	case "json":
		jsonData, err := json.MarshalIndent(webpageData, "", "  ")
		if err != nil {
			return "", fmt.Errorf("JSON序列化失败: %w", err)
		}
		return string(jsonData), nil
	case "html":
		return fmt.Sprintf("URL: %s\n标题: %s\n描述: %s\n状态码: %d\nHTML内容:\n%s",
			webpageData.URL, webpageData.Title, webpageData.Description,
			webpageData.StatusCode, webpageData.Content), nil
	case "markdown":
		return fmt.Sprintf("# %s\n\n**URL:** %s  \n**状态码:** %d  \n**描述:** %s\n\n## 内容\n\n%s",
			webpageData.Title, webpageData.URL, webpageData.StatusCode,
			webpageData.Description, webpageData.Content), nil
	default: // text
		return fmt.Sprintf("URL: %s\n标题: %s\n描述: %s\n状态码: %d\n内容: %s",
			webpageData.URL, webpageData.Title, webpageData.Description,
			webpageData.StatusCode, webpageData.Content), nil
	}
}

// Parameters 返回工具接受的参数规范
func (w *WebpageTool) Parameters() map[string]interfaces.ParameterSpec {
	return map[string]interfaces.ParameterSpec{
		"url": {
			Type:        "string",
			Description: "要获取内容的网页URL地址",
			Required:    true,
		},
		"headers": {
			Type:        "object",
			Description: "可选的HTTP请求头",
			Required:    false,
		},
		"max_length": {
			Type:        "number",
			Description: "返回内容的最大字符数 (默认: 5000)",
			Required:    false,
			Default:     5000,
		},
		"start_index": {
			Type:        "number",
			Description: "内容开始的字符索引 (默认: 0)",
			Required:    false,
			Default:     0,
		},
		"format": {
			Type:        "string",
			Description: "返回格式: html, text, markdown, json (默认: text)",
			Required:    false,
			Default:     "text",
			Enum:        []interface{}{"html", "text", "markdown", "json"},
		},
	}
}

type webpageResponse struct {
	URL         string `json:"url"`
	Title       string `json:"title"`
	Description string `json:"description"`
	StatusCode  int    `json:"status_code"`
	Content     string `json:"content_preview"`
	ContentType string `json:"content_type"`
}

// fetchWebpageData 从指定URL获取网页数据（向后兼容）
func (w *WebpageTool) fetchWebpageData(ctx context.Context, inputURL string) (*webpageResponse, error) {
	params := WebpageParam{
		Url:       inputURL,
		MaxLength: 5000,
		Format:    "text",
	}
	return w.fetchWebpageDataWithParams(ctx, params)
}

// fetchWebpageDataWithParams 使用完整参数从指定URL获取网页数据
func (w *WebpageTool) fetchWebpageDataWithParams(ctx context.Context, params WebpageParam) (*webpageResponse, error) {
	// 验证和清理URL
	cleanURL, err := w.validateAndCleanURL(params.Url)
	if err != nil {
		return nil, fmt.Errorf("无效的URL: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", cleanURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置默认请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")

	// 设置自定义请求头
	for key, value := range params.Headers {
		req.Header.Set(key, value)
	}

	// 发送请求
	resp, err := w.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode >= 400 {
		return nil, fmt.Errorf("HTTP错误: %d %s", resp.StatusCode, resp.Status)
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	// 检查内容类型
	contentType := resp.Header.Get("Content-Type")

	// 处理JSON内容
	if strings.Contains(strings.ToLower(contentType), "application/json") {
		content := string(body)
		content = w.applyLengthLimits(content, params.MaxLength, params.StartIndex)

		return &webpageResponse{
			URL:         cleanURL,
			Title:       "JSON数据",
			Description: fmt.Sprintf("内容类型: %s", contentType),
			StatusCode:  resp.StatusCode,
			Content:     content,
			ContentType: contentType,
		}, nil
	}

	// 处理非HTML内容
	if !strings.Contains(strings.ToLower(contentType), "text/html") {
		content := string(body)
		content = w.applyLengthLimits(content, params.MaxLength, params.StartIndex)

		return &webpageResponse{
			URL:         cleanURL,
			Title:       "非HTML内容",
			Description: fmt.Sprintf("内容类型: %s", contentType),
			StatusCode:  resp.StatusCode,
			Content:     content,
			ContentType: contentType,
		}, nil
	}

	// 解析HTML内容
	htmlContent := string(body)
	title := w.extractTitleFromHTML(htmlContent)
	description := w.extractDescriptionFromHTML(htmlContent)

	var content string
	switch params.Format {
	case "html":
		content = w.applyLengthLimits(htmlContent, params.MaxLength, params.StartIndex)
	case "markdown":
		content = w.extractContentAsMarkdown(htmlContent)
		content = w.applyLengthLimits(content, params.MaxLength, params.StartIndex)
	default: // text
		content = w.extractContent(htmlContent)
		content = w.applyLengthLimits(content, params.MaxLength, params.StartIndex)
	}

	return &webpageResponse{
		URL:         cleanURL,
		Title:       title,
		Description: description,
		StatusCode:  resp.StatusCode,
		Content:     content,
		ContentType: contentType,
	}, nil
}

// validateAndCleanURL 验证和清理URL
func (w *WebpageTool) validateAndCleanURL(inputURL string) (string, error) {
	// 去除前后空格
	inputURL = strings.TrimSpace(inputURL)

	// 如果没有协议，添加https://
	if !strings.HasPrefix(inputURL, "http://") && !strings.HasPrefix(inputURL, "https://") {
		inputURL = "https://" + inputURL
	}

	// 验证URL格式
	parsedURL, err := url.Parse(inputURL)
	if err != nil {
		return "", err
	}

	// 检查是否有主机名
	if parsedURL.Host == "" {
		return "", fmt.Errorf("URL缺少主机名")
	}

	// 安全检查：防止访问私有IP地址
	if err := w.checkPrivateIP(parsedURL.Host); err != nil {
		return "", err
	}

	return parsedURL.String(), nil
}

// checkPrivateIP 检查是否为私有IP地址，防止SSRF攻击
func (w *WebpageTool) checkPrivateIP(host string) error {
	// 提取主机名（去除端口）
	hostname := host
	if strings.Contains(host, ":") {
		var err error
		hostname, _, err = net.SplitHostPort(host)
		if err != nil {
			return fmt.Errorf("无效的主机格式: %w", err)
		}
	}

	// 尝试解析IP地址
	ip := net.ParseIP(hostname)
	if ip != nil {
		// 检查是否为私有IP
		if w.isPrivateIP(ip) {
			return fmt.Errorf("禁止访问私有IP地址 %s，这是为了防止安全漏洞", hostname)
		}
	} else {
		// 如果不是IP地址，尝试DNS解析
		ips, err := net.LookupIP(hostname)
		if err == nil {
			for _, resolvedIP := range ips {
				if w.isPrivateIP(resolvedIP) {
					return fmt.Errorf("域名 %s 解析到私有IP地址 %s，禁止访问", hostname, resolvedIP.String())
				}
			}
		}
	}

	return nil
}

// isPrivateIP 检查IP是否为私有地址
func (w *WebpageTool) isPrivateIP(ip net.IP) bool {
	// 检查IPv4私有地址范围
	if ip.To4() != nil {
		// 10.0.0.0/8
		if ip[0] == 10 {
			return true
		}
		// **********/12
		if ip[0] == 172 && ip[1] >= 16 && ip[1] <= 31 {
			return true
		}
		// ***********/16
		if ip[0] == 192 && ip[1] == 168 {
			return true
		}
		// *********/8 (localhost)
		if ip[0] == 127 {
			return true
		}
		// ***********/16 (link-local)
		if ip[0] == 169 && ip[1] == 254 {
			return true
		}
	}

	// 检查IPv6私有地址
	if ip.To16() != nil {
		// ::1 (localhost)
		if ip.Equal(net.IPv6loopback) {
			return true
		}
		// fe80::/10 (link-local)
		if ip[0] == 0xfe && (ip[1]&0xc0) == 0x80 {
			return true
		}
		// fc00::/7 (unique local)
		if (ip[0] & 0xfe) == 0xfc {
			return true
		}
	}

	return false
}

// applyLengthLimits 应用长度限制
func (w *WebpageTool) applyLengthLimits(text string, maxLength, startIndex int) string {
	if maxLength <= 0 {
		maxLength = 5000
	}

	runes := []rune(text)
	if startIndex >= len(runes) {
		return ""
	}

	end := startIndex + maxLength
	if end > len(runes) {
		end = len(runes)
	}

	return string(runes[startIndex:end])
}

// extractTitleFromHTML 从HTML中提取标题（使用HTML解析器）
func (w *WebpageTool) extractTitleFromHTML(htmlContent string) string {
	doc, err := html.Parse(strings.NewReader(htmlContent))
	if err != nil {
		// 回退到正则表达式
		return w.extractTitle(htmlContent)
	}

	title := w.findTitleInNode(doc)
	if title != "" {
		return w.cleanText(title)
	}

	return "未找到标题"
}

// findTitleInNode 在HTML节点中查找title标签
func (w *WebpageTool) findTitleInNode(n *html.Node) string {
	if n.Type == html.ElementNode && n.Data == "title" {
		return w.getTextContent(n)
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		if title := w.findTitleInNode(c); title != "" {
			return title
		}
	}

	return ""
}

// extractTitle 从HTML中提取标题（正则表达式方式，作为备用）
func (w *WebpageTool) extractTitle(html string) string {
	titleRegex := regexp.MustCompile(`(?i)<title[^>]*>([^<]*)</title>`)
	matches := titleRegex.FindStringSubmatch(html)
	if len(matches) > 1 {
		return w.cleanText(matches[1])
	}
	return "未找到标题"
}

// extractDescriptionFromHTML 从HTML中提取描述（使用HTML解析器）
func (w *WebpageTool) extractDescriptionFromHTML(htmlContent string) string {
	doc, err := html.Parse(strings.NewReader(htmlContent))
	if err != nil {
		// 回退到正则表达式
		return w.extractDescription(htmlContent)
	}

	description := w.findMetaDescription(doc)
	if description != "" {
		return w.cleanText(description)
	}

	return "未找到描述"
}

// findMetaDescription 在HTML节点中查找meta description
func (w *WebpageTool) findMetaDescription(n *html.Node) string {
	if n.Type == html.ElementNode && n.Data == "meta" {
		var name, property, content string
		for _, attr := range n.Attr {
			switch strings.ToLower(attr.Key) {
			case "name":
				name = strings.ToLower(attr.Val)
			case "property":
				property = strings.ToLower(attr.Val)
			case "content":
				content = attr.Val
			}
		}

		if (name == "description" || property == "og:description") && content != "" {
			return content
		}
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		if desc := w.findMetaDescription(c); desc != "" {
			return desc
		}
	}

	return ""
}

// extractDescription 从HTML中提取描述（正则表达式方式，作为备用）
func (w *WebpageTool) extractDescription(html string) string {
	// 尝试提取meta description
	descRegex := regexp.MustCompile(`(?i)<meta[^>]*name=["']description["'][^>]*content=["']([^"']*)["'][^>]*>`)
	matches := descRegex.FindStringSubmatch(html)
	if len(matches) > 1 {
		return w.cleanText(matches[1])
	}

	// 尝试提取og:description
	ogDescRegex := regexp.MustCompile(`(?i)<meta[^>]*property=["']og:description["'][^>]*content=["']([^"']*)["'][^>]*>`)
	matches = ogDescRegex.FindStringSubmatch(html)
	if len(matches) > 1 {
		return w.cleanText(matches[1])
	}

	return "未找到描述"
}

// extractContent 从HTML中提取纯文本内容（基于fetch-mcp的简化方法）
func (w *WebpageTool) extractContent(htmlContent string) string {
	doc, err := html.Parse(strings.NewReader(htmlContent))
	if err != nil {
		w.logger.Errorf("解析HTML失败: %v", err)
		return ""
	}

	// 移除不需要的标签
	w.removeUnwantedElements(doc)

	// 找到body节点，如果没有则使用整个文档
	body := w.findBodyNode(doc)
	if body == nil {
		body = doc
	}

	// 提取文本内容
	text := w.extractTextContent(body)

	// 标准化空白字符
	return w.normalizeWhitespace(text)
}

// removeUnwantedElements 移除不需要的HTML元素（基于fetch-mcp方法）
func (w *WebpageTool) removeUnwantedElements(n *html.Node) {
	// 使用倒序遍历子节点，这样删除时不会影响索引
	for c := n.LastChild; c != nil; c = c.PrevSibling {
		// 递归处理子节点
		w.removeUnwantedElements(c)

		// 如果子节点是需要移除的标签，则从父节点中移除
		if c.Type == html.ElementNode && unwantedTags[c.Data] {
			n.RemoveChild(c)
		}
	}
}

// findBodyNode 查找body节点
func (w *WebpageTool) findBodyNode(n *html.Node) *html.Node {
	if n.Type == html.ElementNode && n.Data == "body" {
		return n
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		if body := w.findBodyNode(c); body != nil {
			return body
		}
	}

	return nil
}

// extractTextContent 从节点中提取文本内容（基于fetch-mcp方法）
func (w *WebpageTool) extractTextContent(n *html.Node) string {
	var builder strings.Builder

	var traverse func(*html.Node)
	traverse = func(node *html.Node) {
		if node.Type == html.TextNode {
			builder.WriteString(node.Data)
		} else if node.Type == html.ElementNode {
			// 在块级元素前后添加空格，以保持文本分离
			if w.isBlockElement(node.Data) {
				builder.WriteString(" ")
			}

			for c := node.FirstChild; c != nil; c = c.NextSibling {
				traverse(c)
			}

			if w.isBlockElement(node.Data) {
				builder.WriteString(" ")
			}
		}
	}

	traverse(n)
	return builder.String()
}

// isBlockElement 判断是否为块级元素
func (w *WebpageTool) isBlockElement(tagName string) bool {
	blockElements := map[string]bool{
		"div": true, "p": true, "h1": true, "h2": true, "h3": true,
		"h4": true, "h5": true, "h6": true, "article": true, "section": true,
		"header": true, "footer": true, "main": true, "aside": true,
		"ul": true, "ol": true, "li": true, "blockquote": true,
		"pre": true, "table": true, "tr": true, "td": true, "th": true,
	}
	return blockElements[tagName]
}

// normalizeWhitespace 标准化空白字符（基于fetch-mcp方法）
func (w *WebpageTool) normalizeWhitespace(text string) string {
	// 将多个空白字符替换为单个空格
	spaceRegex := regexp.MustCompile(`\s+`)
	normalized := spaceRegex.ReplaceAllString(text, " ")

	// 去除首尾空白
	return strings.TrimSpace(normalized)
}

// cleanText 清理文本内容
func (w *WebpageTool) cleanText(text string) string {
	// 替换HTML实体
	text = strings.ReplaceAll(text, "&nbsp;", " ")
	text = strings.ReplaceAll(text, "&amp;", "&")
	text = strings.ReplaceAll(text, "&lt;", "<")
	text = strings.ReplaceAll(text, "&gt;", ">")
	text = strings.ReplaceAll(text, "&quot;", "\"")
	text = strings.ReplaceAll(text, "&#39;", "'")

	// 移除多余的空白字符
	spaceRegex := regexp.MustCompile(`\s+`)
	text = spaceRegex.ReplaceAllString(text, " ")

	return strings.TrimSpace(text)
}

// extractContentAsMarkdown 将HTML内容转换为Markdown格式（简化版本）
func (w *WebpageTool) extractContentAsMarkdown(htmlContent string) string {
	doc, err := html.Parse(strings.NewReader(htmlContent))
	if err != nil {
		w.logger.Errorf("解析HTML失败: %v", err)
		return ""
	}

	// 移除不需要的标签
	w.removeUnwantedElements(doc)

	// 找到body节点，如果没有则使用整个文档
	body := w.findBodyNode(doc)
	if body == nil {
		body = doc
	}

	return w.convertToMarkdown(body)
}

// convertToMarkdown 将HTML节点转换为Markdown
func (w *WebpageTool) convertToMarkdown(n *html.Node) string {
	var builder strings.Builder
	w.convertNodeToMarkdown(n, &builder, 0)
	return strings.TrimSpace(builder.String())
}

// convertNodeToMarkdown 递归转换HTML节点为Markdown
func (w *WebpageTool) convertNodeToMarkdown(n *html.Node, builder *strings.Builder, depth int) {
	if n.Type == html.TextNode {
		text := strings.TrimSpace(n.Data)
		if text != "" {
			builder.WriteString(text)
		}
		return
	}

	if n.Type == html.ElementNode {
		switch n.Data {
		case "h1":
			builder.WriteString("\n# ")
			w.processChildren(n, builder, depth)
			builder.WriteString("\n\n")
		case "h2":
			builder.WriteString("\n## ")
			w.processChildren(n, builder, depth)
			builder.WriteString("\n\n")
		case "h3":
			builder.WriteString("\n### ")
			w.processChildren(n, builder, depth)
			builder.WriteString("\n\n")
		case "h4":
			builder.WriteString("\n#### ")
			w.processChildren(n, builder, depth)
			builder.WriteString("\n\n")
		case "h5":
			builder.WriteString("\n##### ")
			w.processChildren(n, builder, depth)
			builder.WriteString("\n\n")
		case "h6":
			builder.WriteString("\n###### ")
			w.processChildren(n, builder, depth)
			builder.WriteString("\n\n")
		case "p":
			builder.WriteString("\n")
			w.processChildren(n, builder, depth)
			builder.WriteString("\n\n")
		case "br":
			builder.WriteString("\n")
		case "strong", "b":
			builder.WriteString("**")
			w.processChildren(n, builder, depth)
			builder.WriteString("**")
		case "em", "i":
			builder.WriteString("*")
			w.processChildren(n, builder, depth)
			builder.WriteString("*")
		case "a":
			href := w.getAttr(n, "href")
			if href != "" {
				builder.WriteString("[")
				w.processChildren(n, builder, depth)
				builder.WriteString("](")
				builder.WriteString(href)
				builder.WriteString(")")
			} else {
				w.processChildren(n, builder, depth)
			}
		case "ul":
			builder.WriteString("\n")
			w.processChildren(n, builder, depth)
			builder.WriteString("\n")
		case "ol":
			builder.WriteString("\n")
			w.processChildren(n, builder, depth)
			builder.WriteString("\n")
		case "li":
			builder.WriteString("- ")
			w.processChildren(n, builder, depth)
			builder.WriteString("\n")
		case "blockquote":
			builder.WriteString("\n> ")
			w.processChildren(n, builder, depth)
			builder.WriteString("\n\n")
		case "code":
			builder.WriteString("`")
			w.processChildren(n, builder, depth)
			builder.WriteString("`")
		case "pre":
			builder.WriteString("\n```\n")
			w.processChildren(n, builder, depth)
			builder.WriteString("\n```\n\n")
		default:
			w.processChildren(n, builder, depth)
		}
	}
}

// processChildren 处理子节点
func (w *WebpageTool) processChildren(n *html.Node, builder *strings.Builder, depth int) {
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		w.convertNodeToMarkdown(c, builder, depth+1)
	}
}

// getAttr 获取节点属性值
func (w *WebpageTool) getAttr(n *html.Node, key string) string {
	for _, attr := range n.Attr {
		if attr.Key == key {
			return attr.Val
		}
	}
	return ""
}

// getTextContent 获取节点的文本内容
func (w *WebpageTool) getTextContent(n *html.Node) string {
	var builder strings.Builder
	w.extractTextRecursive(n, &builder)
	return strings.TrimSpace(builder.String())
}

// extractTextRecursive 递归提取文本内容
func (w *WebpageTool) extractTextRecursive(n *html.Node, builder *strings.Builder) {
	if n.Type == html.TextNode {
		builder.WriteString(n.Data)
	}
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		w.extractTextRecursive(c, builder)
	}
}

// truncateText 截取文本到指定长度
func (w *WebpageTool) truncateText(text string, maxLength int) string {
	runes := []rune(text)
	if len(runes) <= maxLength {
		return text
	}

	// 尝试在单词边界截断
	truncated := string(runes[:maxLength])
	lastSpace := strings.LastIndex(truncated, " ")
	if lastSpace > maxLength/2 {
		truncated = truncated[:lastSpace]
	}

	return truncated + "..."
}
