package tools

import (
	"context"
	"fmt"
	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/zeromicro/go-zero/core/logx"
	"math"
	"sort"
	"strings"
)

// ToolDiscovery provides semantic tool discovery capabilities
type ToolDiscovery struct {
	ctx         context.Context
	logger      logx.Logger
	tools       *Tools
	embedder    interfaces.Embedder
	vectorStore interfaces.VectorStore
}

// ToolMatch represents a tool match with similarity score
type ToolMatch struct {
	Tool       interfaces.Tool
	Similarity float32
	Reason     string
}

// NewToolDiscovery creates a new tool discovery service
func NewToolDiscovery(ctx context.Context, tools *Tools, embedder interfaces.Embedder, vectorStore interfaces.VectorStore) *ToolDiscovery {
	return &ToolDiscovery{
		ctx:         ctx,
		logger:      logx.WithContext(ctx),
		tools:       tools,
		embedder:    embedder,
		vectorStore: vectorStore,
	}
}

// DiscoverTools finds the most relevant tools for a given query
func (td *ToolDiscovery) DiscoverTools(query string, maxTools int) ([]ToolMatch, error) {
	if maxTools <= 0 {
		maxTools = 5 // Default to top 5 tools
	}

	// Get all available tools
	allToolNames := make([]string, 0, len(td.tools.allTools))
	for name := range td.tools.allTools {
		allToolNames = append(allToolNames, name)
	}

	if len(allToolNames) == 0 {
		return []ToolMatch{}, nil
	}

	// Use both semantic and keyword-based matching
	semanticMatches := td.semanticMatch(query, allToolNames, maxTools)
	keywordMatches := td.keywordMatch(query, allToolNames, maxTools)

	// Combine and deduplicate matches
	combinedMatches := td.combineMatches(semanticMatches, keywordMatches)

	// Sort by similarity score and limit results
	sort.Slice(combinedMatches, func(i, j int) bool {
		return combinedMatches[i].Similarity > combinedMatches[j].Similarity
	})

	if len(combinedMatches) > maxTools {
		combinedMatches = combinedMatches[:maxTools]
	}

	return combinedMatches, nil
}

// semanticMatch performs semantic similarity matching using embeddings
func (td *ToolDiscovery) semanticMatch(query string, toolNames []string, maxTools int) []ToolMatch {
	matches := make([]ToolMatch, 0)

	// If embedder is not available, skip semantic matching
	if td.embedder == nil {
		return matches
	}

	// Generate embedding for the query
	queryEmbedding, err := td.embedder.Embed(td.ctx, query)
	if err != nil {
		td.logger.Errorf("Failed to generate query embedding: %v", err)
		return matches
	}

	// Compare with tool descriptions
	for _, toolName := range toolNames {
		tool, exists := td.tools.allTools[toolName]
		if !exists {
			continue
		}

		// Generate embedding for tool description
		toolDescription := tool.Description()
		toolEmbedding, err := td.embedder.Embed(td.ctx, toolDescription)
		if err != nil {
			td.logger.Errorf("Failed to generate embedding for tool %s: %v", toolName, err)
			continue
		}

		// Calculate similarity
		similarity := td.cosineSimilarity(queryEmbedding, toolEmbedding)

		// Only include tools with reasonable similarity
		if similarity > 0.3 {
			matches = append(matches, ToolMatch{
				Tool:       tool,
				Similarity: similarity,
				Reason:     fmt.Sprintf("Semantic similarity: %.2f", similarity),
			})
		}
	}

	return matches
}

// keywordMatch performs keyword-based matching
func (td *ToolDiscovery) keywordMatch(query string, toolNames []string, maxTools int) []ToolMatch {
	matches := make([]ToolMatch, 0)

	queryLower := strings.ToLower(query)
	queryWords := strings.Fields(queryLower)

	for _, toolName := range toolNames {
		tool, exists := td.tools.allTools[toolName]
		if !exists {
			continue
		}

		toolNameLower := strings.ToLower(toolName)
		toolDescLower := strings.ToLower(tool.Description())

		score := float32(0.0)
		matchedWords := make([]string, 0)

		// Check for exact tool name match
		if strings.Contains(queryLower, toolNameLower) {
			score += 0.8
			matchedWords = append(matchedWords, toolName)
		}

		// Check for keyword matches in description
		for _, word := range queryWords {
			if len(word) < 3 { // Skip very short words
				continue
			}

			if strings.Contains(toolNameLower, word) {
				score += 0.6
				matchedWords = append(matchedWords, word)
			} else if strings.Contains(toolDescLower, word) {
				score += 0.3
				matchedWords = append(matchedWords, word)
			}
		}

		// Only include tools with some keyword match
		if score > 0 {
			reason := fmt.Sprintf("Keyword match (%.2f): %s", score, strings.Join(matchedWords, ", "))
			matches = append(matches, ToolMatch{
				Tool:       tool,
				Similarity: score,
				Reason:     reason,
			})
		}
	}

	return matches
}

// combineMatches combines semantic and keyword matches, avoiding duplicates
func (td *ToolDiscovery) combineMatches(semanticMatches, keywordMatches []ToolMatch) []ToolMatch {
	matchMap := make(map[string]ToolMatch)

	// Add semantic matches
	for _, match := range semanticMatches {
		toolName := td.getToolName(match.Tool)
		matchMap[toolName] = match
	}

	// Add keyword matches, combining scores if tool already exists
	for _, match := range keywordMatches {
		toolName := td.getToolName(match.Tool)
		if existing, exists := matchMap[toolName]; exists {
			// Combine scores (weighted average)
			combinedScore := existing.Similarity*0.7 + match.Similarity*0.3
			combinedReason := fmt.Sprintf("%s; %s", existing.Reason, match.Reason)
			matchMap[toolName] = ToolMatch{
				Tool:       match.Tool,
				Similarity: combinedScore,
				Reason:     combinedReason,
			}
		} else {
			matchMap[toolName] = match
		}
	}

	// Convert map back to slice
	result := make([]ToolMatch, 0, len(matchMap))
	for _, match := range matchMap {
		result = append(result, match)
	}

	return result
}

// getToolName extracts the tool name (this is a simple implementation)
func (td *ToolDiscovery) getToolName(tool interfaces.Tool) string {
	// This is a simple way to get tool name - in a real implementation,
	// you might want to add a Name() method to the Tool interface
	for name, t := range td.tools.allTools {
		if t == tool {
			return name
		}
	}
	return "unknown"
}

// cosineSimilarity calculates cosine similarity between two vectors
func (td *ToolDiscovery) cosineSimilarity(a, b []float32) float32 {
	if len(a) != len(b) {
		return 0
	}

	var dotProduct, normA, normB float32
	for i, valA := range a {
		valB := b[i]
		dotProduct += valA * valB
		normA += valA * valA
		normB += valB * valB
	}

	if normA == 0 || normB == 0 {
		return 0
	}
	return dotProduct / (float32(math.Sqrt(float64(normA))) * float32(math.Sqrt(float64(normB))))
}

// GetToolsContext returns a formatted string describing discovered tools
func (td *ToolDiscovery) GetToolsContext(query string, maxTools int) string {
	matches, err := td.DiscoverTools(query, maxTools)
	if err != nil {
		td.logger.Errorf("Failed to discover tools: %v", err)
		return "No tools available due to discovery error."
	}

	if len(matches) == 0 {
		return "No relevant tools found for this query."
	}

	var ctx strings.Builder
	ctx.WriteString("Relevant tools discovered:\n")

	for i, match := range matches {
		ctx.WriteString(fmt.Sprintf("%d. %s (Score: %.2f)\n",
			i+1,
			td.getToolName(match.Tool),
			match.Similarity))
		ctx.WriteString(fmt.Sprintf("   Description: %s\n", match.Tool.Description()))
		ctx.WriteString(fmt.Sprintf("   Reason: %s\n", match.Reason))
		ctx.WriteString("\n")
	}

	return ctx.String()
}
