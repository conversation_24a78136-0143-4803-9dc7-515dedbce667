package service

import "github.com/run-bigpig/hongdou/internal/consts"

type StructOutput struct {
	DispatchMode       consts.MasterMindDispatchMode `json:"dispatch_mode" description:"dispatch mode"`
	Answer             string                        `json:"answer,omitempty" description:"answer"`
	SubAgents          []AgentConfig                 `json:"sub_agents,omitempty" description:"sub agents"`
	ReviseSystemPrompt string                        `json:"revise_system_prompt,omitempty" description:"revise system prompt"`
}

type AgentConfig struct {
	AgentName    string   `json:"agent_name" description:"agent name"`
	SystemPrompt string   `json:"system_prompt" description:"system prompt"`
	EnabledTools []string `json:"enabled_tools" description:"enable tools list"`
	SubQuestion  string   `json:"sub_question,omitempty" description:"sub question"`
}
