package mastermind

import (
	"context"
	"strings"
	"testing"

	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/run-bigpig/hongdou/internal/pkg/multitenancy"
	"github.com/run-bigpig/hongdou/internal/service/message"
)

// MockMemory implements interfaces.Memory for testing
type MockMemory struct {
	messages []interfaces.Message
}

func (m *MockMemory) AddMessage(ctx context.Context, message interfaces.Message) error {
	m.messages = append(m.messages, message)
	return nil
}

func (m *MockMemory) GetMessages(ctx context.Context, options ...interfaces.GetMessagesOption) ([]interfaces.Message, error) {
	opts := &interfaces.GetMessagesOptions{}
	for _, option := range options {
		option(opts)
	}

	var result []interfaces.Message
	count := 0
	
	for _, msg := range m.messages {
		// Apply role filtering
		if len(opts.Roles) > 0 {
			roleMatch := false
			for _, role := range opts.Roles {
				if msg.Role == role {
					roleMatch = true
					break
				}
			}
			if !roleMatch {
				continue
			}
		}
		
		// Apply query filtering (flexible matching for testing)
		if opts.Query != "" {
			queryLower := strings.ToLower(opts.Query)
			contentLower := strings.ToLower(msg.Content)

			// Check for any word match
			queryWords := strings.Fields(queryLower)
			matched := false
			for _, word := range queryWords {
				if strings.Contains(contentLower, word) {
					matched = true
					break
				}
			}
			if !matched {
				continue
			}
		}
		
		result = append(result, msg)
		count++
		
		// Apply limit
		if opts.Limit > 0 && count >= opts.Limit {
			break
		}
	}
	
	return result, nil
}

func (m *MockMemory) Clear(ctx context.Context) error {
	m.messages = nil
	return nil
}

func TestBuildEnhancedPrompt_Structure(t *testing.T) {
	// Create mock memories
	shortMemory := &MockMemory{
		messages: []interfaces.Message{
			{Role: "user", Content: "Hello, I'm working on a Python project"},
			{Role: "assistant", Content: "Great! I'd be happy to help with your Python project. What specific area do you need assistance with?"},
			{Role: "user", Content: "I need help with data analysis using pandas"},
		},
	}
	
	longMemory := &MockMemory{
		messages: []interfaces.Message{
			{Role: "system", Content: "You are an expert Python developer with specialization in data science and machine learning"},
			{Role: "user", Content: "I prefer using pandas for data manipulation"},
			{Role: "assistant", Content: "I'll remember your preference for pandas. It's an excellent choice for data analysis."},
			{Role: "user", Content: "I work with large datasets regularly"},
		},
	}
	
	// Create context with user information
	ctx := context.Background()
	ctx = multitenancy.WithOrgID(ctx, "test-org")
	ctx = multitenancy.WithUserID(ctx, "test-user")
	
	// Create message services
	shortMemoryMessage := message.NewMessage(ctx, shortMemory)
	longMemoryMessage := message.NewMessage(ctx, longMemory)
	
	// Create mastermind instance
	mastermind := &Mastermind{
		ctx:                ctx,
		shortMemoryMessage: shortMemoryMessage,
		longMemoryMessage:  longMemoryMessage,
	}
	
	// Test prompt building
	question := "How can I optimize pandas operations for large datasets?"
	toolsContext := "Available tools: pandas_profiler, memory_optimizer, performance_analyzer"
	
	prompt := mastermind.buildEnhancedPrompt(question, toolsContext)

	// Debug: Print the actual prompt
	t.Logf("Generated prompt:\n%s", prompt)

	// Verify prompt structure for the optimized user prompt
	t.Run("Contains User Context", func(t *testing.T) {
		if !strings.Contains(prompt, "## USER CONTEXT") {
			t.Error("Prompt should contain user context section")
		}
		if !strings.Contains(prompt, "test-user") && !strings.Contains(prompt, "test-org") {
			t.Error("Prompt should contain user identification")
		}
	})

	t.Run("Contains Memory Context", func(t *testing.T) {
		if !strings.Contains(prompt, "## RELEVANT CONTEXT FROM MEMORY") {
			t.Error("Prompt should contain memory context section")
		}
	})

	t.Run("Contains System Instructions", func(t *testing.T) {
		if !strings.Contains(prompt, "### System Instructions & Guidelines") {
			t.Error("Prompt should contain system instructions section")
		}
		if !strings.Contains(prompt, "expert Python developer") {
			t.Error("Prompt should contain system memory content")
		}
	})

	t.Run("Contains Personal Context", func(t *testing.T) {
		if !strings.Contains(prompt, "### Personal Context & Preferences") {
			t.Error("Prompt should contain personal context section")
		}
		if !strings.Contains(prompt, "pandas for data manipulation") {
			t.Error("Prompt should contain personal memory content")
		}
	})

	t.Run("Contains Recent History", func(t *testing.T) {
		if !strings.Contains(prompt, "## RECENT CONVERSATION HISTORY") {
			t.Error("Prompt should contain recent history section")
		}
		if !strings.Contains(prompt, "Python project") {
			t.Error("Prompt should contain recent conversation content")
		}
	})

	t.Run("Contains Available Tools", func(t *testing.T) {
		if !strings.Contains(prompt, "## AVAILABLE TOOLS & CAPABILITIES") {
			t.Error("Prompt should contain tools section")
		}
		if !strings.Contains(prompt, "pandas_profiler") {
			t.Error("Prompt should contain tools context")
		}
	})

	t.Run("Contains Current Query", func(t *testing.T) {
		if !strings.Contains(prompt, "## CURRENT USER QUERY") {
			t.Error("Prompt should contain current query section")
		}
		if !strings.Contains(prompt, "optimize pandas operations") {
			t.Error("Prompt should contain the user question")
		}
	})

	t.Run("Contains Analysis Instructions", func(t *testing.T) {
		if !strings.Contains(prompt, "## ANALYSIS & RESPONSE INSTRUCTIONS") {
			t.Error("Prompt should contain analysis instructions")
		}
		if !strings.Contains(prompt, "### 1. Context Analysis") {
			t.Error("Prompt should contain step-by-step analysis instructions")
		}
	})

	t.Run("Contains Response Format", func(t *testing.T) {
		if !strings.Contains(prompt, "### 4. Response Format") {
			t.Error("Prompt should contain response format section")
		}
		if !strings.Contains(prompt, "dispatch_mode") {
			t.Error("Prompt should contain JSON format specification")
		}
	})

	t.Run("Contains Dispatch Mode Instructions", func(t *testing.T) {
		if !strings.Contains(prompt, "DIRECT_ANSWER") {
			t.Error("Prompt should contain DIRECT_ANSWER mode description")
		}
		if !strings.Contains(prompt, "AGENT_EXECUTION") {
			t.Error("Prompt should contain AGENT_EXECUTION mode description")
		}
	})
}

func TestPromptBuilder_MemoryIntegration(t *testing.T) {
	// Create mock memory with various message types
	longMemory := &MockMemory{
		messages: []interfaces.Message{
			{Role: "system", Content: "System instruction about pandas optimization and data analysis"},
			{Role: "user", Content: "I need help with pandas dataframes"},
			{Role: "assistant", Content: "I can help you with pandas operations"},
			{Role: "user", Content: "What about machine learning?"},
			{Role: "assistant", Content: "I can also assist with ML algorithms"},
			{Role: "system", Content: "Remember to prioritize pandas performance and data quality"},
		},
	}

	shortMemory := &MockMemory{
		messages: []interfaces.Message{
			{Role: "user", Content: "Hello, I need help with data analysis"},
			{Role: "assistant", Content: "I'd be happy to help with data analysis"},
		},
	}

	ctx := context.Background()
	ctx = multitenancy.WithOrgID(ctx, "test-org")
	ctx = multitenancy.WithUserID(ctx, "test-user")

	mastermind := &Mastermind{
		ctx:                ctx,
		shortMemoryMessage: message.NewMessage(ctx, shortMemory),
		longMemoryMessage:  message.NewMessage(ctx, longMemory),
	}

	prompt := mastermind.buildEnhancedPrompt("pandas optimization question", "test tools")

	t.Run("Memory Integration Works", func(t *testing.T) {
		if !strings.Contains(prompt, "System instruction about pandas optimization") {
			t.Error("Should include system memories")
		}
		if !strings.Contains(prompt, "help with pandas dataframes") {
			t.Error("Should include relevant personal memories")
		}
	})

	t.Run("User Context Included", func(t *testing.T) {
		if !strings.Contains(prompt, "test-user") {
			t.Error("Should include user ID")
		}
		if !strings.Contains(prompt, "test-org") {
			t.Error("Should include organization ID")
		}
	})
}

func TestPromptBuilder_EmptyMemories(t *testing.T) {
	// Test with empty memories
	ctx := context.Background()
	ctx = multitenancy.WithOrgID(ctx, "test-org")

	mastermind := &Mastermind{
		ctx:                ctx,
		shortMemoryMessage: message.NewMessage(ctx, &MockMemory{}),
		longMemoryMessage:  message.NewMessage(ctx, &MockMemory{}),
	}

	prompt := mastermind.buildEnhancedPrompt("test question", "test tools")

	// Should still generate a valid prompt structure even with no memories
	if !strings.Contains(prompt, "## CURRENT USER QUERY") {
		t.Error("Should contain current query section even with empty memories")
	}

	if !strings.Contains(prompt, "test question") {
		t.Error("Should contain the user question")
	}

	if !strings.Contains(prompt, "test tools") {
		t.Error("Should contain the tools context")
	}

	if !strings.Contains(prompt, "## ANALYSIS & RESPONSE INSTRUCTIONS") {
		t.Error("Should contain analysis instructions even with empty memories")
	}
}
