package message

import (
	"context"
	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
)

type Message struct {
	ctx    context.Context
	memory interfaces.Memory
}

func NewMessage(ctx context.Context, memory interfaces.Memory) *Message {
	return &Message{
		ctx:    ctx,
		memory: memory,
	}
}

func (m *Message) AddUserMessage(message string) {
	if m.memory != nil {
		if err := m.memory.AddMessage(m.ctx, interfaces.Message{
			Role:    "user",
			Content: message,
		}); err != nil {
			// Handle error
		}
	}
}

func (m *Message) AddAssistantMessage(message string) {
	if m.memory != nil {
		if err := m.memory.AddMessage(m.ctx, interfaces.Message{
			Role:    "assistant",
			Content: message,
		}); err != nil {
			// Handle error
		}
	}
}

func (m *Message) AddSystemMessage(message string) {
	if m.memory != nil {
		if err := m.memory.AddMessage(m.ctx, interfaces.Message{
			Role:    "system",
			Content: message,
		}); err != nil {
			// Handle error
		}
	}
}
func (m *Message) GetMessages(options ...interfaces.GetMessagesOption) []interfaces.Message {
	if m.memory != nil {
		messages, err := m.memory.GetMessages(m.ctx, options...)
		if err != nil {
			// Handle error
		}
		return messages
	}
	return nil
}
