package memory

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/run-bigpig/hongdou/internal/pkg/multitenancy"
)

// VectorStoreRetriever implements a memory that stores messages in a vector store
type VectorStoreRetriever struct {
	buffer      *ConversationBuffer
	vectorStore interfaces.VectorStore
	mu          sync.RWMutex
}

// RetrieverOption represents an option for configuring the vector store retriever
type RetrieverOption func(*VectorStoreRetriever)

// NewVectorStoreRetriever creates a new vector store retriever memory
func NewVectorStoreRetriever(vectorStore interfaces.VectorStore, options ...RetrieverOption) *VectorStoreRetriever {
	retriever := &VectorStoreRetriever{
		buffer:      NewConversationBuffer(),
		vectorStore: vectorStore,
	}

	for _, option := range options {
		option(retriever)
	}

	return retriever
}

// AddMessage adds a message to the memory with enhanced user and role context
func (v *VectorStoreRetriever) AddMessage(ctx context.Context, message interfaces.Message) error {
	v.mu.Lock()
	defer v.mu.Unlock()

	// Add message to buffer
	if err := v.buffer.AddMessage(ctx, message); err != nil {
		return err
	}

	// Get user and organization context
	orgID, _ := multitenancy.GetOrgID(ctx)
	userID, _ := multitenancy.GetUserID(ctx)
	conversationID, _ := GetConversationID(ctx)

	// Generate timestamp if not provided
	timestamp := time.Now().Unix()
	if ts, ok := message.Metadata["timestamp"]; ok {
		if tsInt, ok := ts.(int64); ok {
			timestamp = tsInt
		} else if tsFloat, ok := ts.(float64); ok {
			timestamp = int64(tsFloat)
		}
	}

	// Create enhanced document with user isolation and role context
	userOrgKey := multitenancy.GetUserOrgKeyWithDefault(ctx, "default", "")
	docID := fmt.Sprintf("%s-%s-%s-%d", userOrgKey, message.Role, conversationID, timestamp)

	// Enhanced metadata for better retrieval and isolation
	metadata := map[string]interface{}{
		"role":            message.Role,
		"timestamp":       timestamp,
		"org_id":          orgID,
		"user_id":         userID,
		"conversation_id": conversationID,
		"user_org_key":    userOrgKey,
		"memory_type":     "long_term", // Mark as long-term memory
	}

	// Copy original metadata while preserving enhanced fields
	for k, v := range message.Metadata {
		if _, exists := metadata[k]; !exists {
			metadata[k] = v
		}
	}

	doc := interfaces.Document{
		ID:       docID,
		Content:  message.Content,
		Metadata: metadata,
	}

	if err := v.vectorStore.Store(ctx, []interfaces.Document{doc}); err != nil {
		return fmt.Errorf("failed to store message in vector store: %w", err)
	}

	return nil
}

// GetMessages retrieves messages from the memory with enhanced user and role filtering
func (v *VectorStoreRetriever) GetMessages(ctx context.Context, options ...interfaces.GetMessagesOption) ([]interfaces.Message, error) {
	v.mu.RLock()
	defer v.mu.RUnlock()

	// Parse options
	opts := &interfaces.GetMessagesOptions{}
	for _, option := range options {
		option(opts)
	}

	// If no query is provided, return messages from buffer
	if opts.Query == "" {
		return v.buffer.GetMessages(ctx, options...)
	}

	// Build user-specific filters for isolation
	filters := v.buildUserContextFilters(ctx, opts)

	// Set default limit if not specified
	limit := opts.Limit
	if limit <= 0 {
		limit = 10
	}

	// Search for relevant messages in vector store with user isolation
	searchOptions := []interfaces.SearchOption{
		interfaces.WithFilters(filters),
	}

	// Add minimum score if specified in options
	if opts.MinScore > 0 {
		searchOptions = append(searchOptions, interfaces.WithMinScore(opts.MinScore))
	} else {
		searchOptions = append(searchOptions, interfaces.WithMinScore(0.1)) // Default minimum relevance threshold
	}

	results, err := v.vectorStore.Search(ctx, opts.Query, limit, searchOptions...)
	if err != nil {
		return nil, fmt.Errorf("failed to search vector store: %w", err)
	}

	// Convert search results to messages with enhanced scoring
	var messages []interfaces.Message
	for _, result := range results {
		role, _ := result.Document.Metadata["role"].(string)
		timestamp, _ := result.Document.Metadata["timestamp"].(float64)
		userID, _ := result.Document.Metadata["user_id"].(string)
		orgID, _ := result.Document.Metadata["org_id"].(string)
		conversationID, _ := result.Document.Metadata["conversation_id"].(string)

		// Apply role filtering if specified
		if len(opts.Roles) > 0 {
			roleMatch := false
			for _, filterRole := range opts.Roles {
				if role == filterRole {
					roleMatch = true
					break
				}
			}
			if !roleMatch {
				continue
			}
		}

		// Calculate enhanced relevance score considering user context
		enhancedScore := v.calculateEnhancedScore(ctx, result, role)

		metadata := map[string]interface{}{
			"timestamp":       timestamp,
			"score":           result.Score,
			"enhanced_score":  enhancedScore,
			"user_id":         userID,
			"org_id":          orgID,
			"conversation_id": conversationID,
		}

		// Copy additional metadata
		for k, v := range result.Document.Metadata {
			if _, exists := metadata[k]; !exists {
				metadata[k] = v
			}
		}

		messages = append(messages, interfaces.Message{
			Role:     role,
			Content:  result.Document.Content,
			Metadata: metadata,
		})
	}

	return messages, nil
}

// Clear clears the memory with enhanced user-specific deletion
func (v *VectorStoreRetriever) Clear(ctx context.Context) error {
	v.mu.Lock()
	defer v.mu.Unlock()

	// Get conversation ID with user context
	conversationID, err := getConversationID(ctx)
	if err != nil {
		return err
	}

	// Clear buffer
	if err := v.buffer.Clear(ctx); err != nil {
		return err
	}

	// TODO: Implement user-specific deletion from vector store
	// This would require querying by user_org_key and conversation_id filters
	// and then deleting the matching documents
	fmt.Printf("Warning: Messages for conversation %s not deleted from vector store (enhanced deletion not yet implemented)\n", conversationID)

	return nil
}

// buildUserContextFilters creates filters for user-specific memory isolation
func (v *VectorStoreRetriever) buildUserContextFilters(ctx context.Context, opts *interfaces.GetMessagesOptions) map[string]interface{} {
	filters := make(map[string]interface{})

	// Get user-org key for isolation
	userOrgKey := multitenancy.GetUserOrgKeyWithDefault(ctx, "default", "")

	// Always filter by user-org key for isolation
	filters["user_org_key"] = map[string]interface{}{
		"operator": "equals",
		"value":    userOrgKey,
	}

	// Add memory type filter to focus on long-term memories
	filters["memory_type"] = map[string]interface{}{
		"operator": "equals",
		"value":    "long_term",
	}

	return filters
}

// calculateEnhancedScore computes relevance score considering user context and role
func (v *VectorStoreRetriever) calculateEnhancedScore(ctx context.Context, result interfaces.SearchResult, role string) float32 {
	baseScore := result.Score

	// Get current user context
	currentUserID, _ := multitenancy.GetUserID(ctx)
	currentOrgID, _ := multitenancy.GetOrgID(ctx)

	// Get document user context
	docUserID, _ := result.Document.Metadata["user_id"].(string)
	docOrgID, _ := result.Document.Metadata["org_id"].(string)

	// User context matching bonus
	userBonus := float32(0.0)
	if currentUserID != "" && docUserID == currentUserID {
		userBonus += 0.2 // 20% bonus for same user
	}
	if currentOrgID != "" && docOrgID == currentOrgID {
		userBonus += 0.1 // 10% bonus for same organization
	}

	// Role-based scoring adjustments
	roleBonus := float32(0.0)
	switch role {
	case "assistant":
		roleBonus = 0.1 // Assistant messages are generally important
	case "system":
		roleBonus = 0.15 // System messages often contain important context
	case "user":
		roleBonus = 0.05 // User messages provide context
	}

	// Time decay factor (newer memories are more relevant)
	timeBonus := float32(0.0)
	if timestamp, ok := result.Document.Metadata["timestamp"].(float64); ok {
		now := float64(time.Now().Unix())
		daysSince := (now - timestamp) / (24 * 3600)
		if daysSince < 1 {
			timeBonus = 0.1 // Recent memories get bonus
		} else if daysSince < 7 {
			timeBonus = 0.05 // Week-old memories get smaller bonus
		}
	}

	// Calculate final enhanced score
	enhancedScore := baseScore + userBonus + roleBonus + timeBonus

	// Ensure score doesn't exceed 1.0
	if enhancedScore > 1.0 {
		enhancedScore = 1.0
	}

	return enhancedScore
}
