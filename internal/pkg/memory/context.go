package memory

import (
	"context"
	"fmt"
	"github.com/run-bigpig/hongdou/internal/pkg/multitenancy"
)

// Key type for context values
type contextKey string

// ConversationIDKey is the key used to store conversation ID in context
const ConversationIDKey contextKey = "conversation_id"

// WithConversationID adds a conversation ID to the context
func WithConversationID(ctx context.Context, conversationID string) context.Context {
	return context.WithValue(ctx, ConversationIDKey, conversationID)
}

// GetConversationID retrieves the conversation ID from the context
func GetConversationID(ctx context.Context) (string, bool) {
	id, ok := ctx.Value(ConversationIDKey).(string)
	return id, ok
}

// Helper function to get conversation ID from context with user isolation
func getConversationKey(prefix string, ctx context.Context) (string, error) {
	// Get user-org composite key for isolation
	userOrgKey, err := multitenancy.GetUserOrgKey(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get user-org key: %w", err)
	}

	// Get conversation ID from context
	conversationID, ok := GetConversationID(ctx)
	if !ok {
		return "", fmt.<PERSON><PERSON>rf("conversation ID not found in context")
	}

	// Combine user-org key and conversation ID for complete isolation
	return fmt.Sprintf("%s%s:%s", prefix, userOrgKey, conversationID), nil
}
