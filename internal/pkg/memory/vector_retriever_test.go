package memory

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/run-bigpig/hongdou/internal/pkg/multitenancy"
)

// MockVectorStore implements interfaces.VectorStore for testing
type MockVectorStore struct {
	documents []interfaces.Document
	searches  []string
}

func (m *MockVectorStore) Store(ctx context.Context, documents []interfaces.Document, options ...interfaces.StoreOption) error {
	m.documents = append(m.documents, documents...)
	return nil
}

func (m *MockVectorStore) Search(ctx context.Context, query string, limit int, options ...interfaces.SearchOption) ([]interfaces.SearchResult, error) {
	m.searches = append(m.searches, query)
	
	// Simple mock search - return documents that contain the query
	var results []interfaces.SearchResult
	for _, doc := range m.documents {
		if len(results) >= limit {
			break
		}
		// Simple relevance scoring for testing
		score := float32(0.8)
		results = append(results, interfaces.SearchResult{
			Document: doc,
			Score:    score,
		})
	}
	return results, nil
}

func (m *MockVectorStore) SearchByVector(ctx context.Context, vector []float32, limit int, options ...interfaces.SearchOption) ([]interfaces.SearchResult, error) {
	return m.Search(ctx, "vector_search", limit, options...)
}

func (m *MockVectorStore) Delete(ctx context.Context, ids []string, options ...interfaces.DeleteOption) error {
	return nil
}

func (m *MockVectorStore) Get(ctx context.Context, ids []string) ([]interfaces.Document, error) {
	return nil, nil
}

func TestVectorStoreRetriever_UserIsolation(t *testing.T) {
	// Create mock vector store
	mockStore := &MockVectorStore{}
	
	// Create retriever
	retriever := NewVectorStoreRetriever(mockStore)
	
	// Test user 1 context
	ctx1 := context.Background()
	ctx1 = multitenancy.WithOrgID(ctx1, "org1")
	ctx1 = multitenancy.WithUserID(ctx1, "user1")
	ctx1 = WithConversationID(ctx1, "conv1")
	
	// Test user 2 context
	ctx2 := context.Background()
	ctx2 = multitenancy.WithOrgID(ctx2, "org1")
	ctx2 = multitenancy.WithUserID(ctx2, "user2")
	ctx2 = WithConversationID(ctx2, "conv2")
	
	// Add messages for user 1
	msg1 := interfaces.Message{
		Role:    "user",
		Content: "Hello from user 1",
		Metadata: map[string]interface{}{
			"timestamp": time.Now().Unix(),
		},
	}
	
	err := retriever.AddMessage(ctx1, msg1)
	if err != nil {
		t.Fatalf("Failed to add message for user 1: %v", err)
	}
	
	// Add messages for user 2
	msg2 := interfaces.Message{
		Role:    "user",
		Content: "Hello from user 2",
		Metadata: map[string]interface{}{
			"timestamp": time.Now().Unix(),
		},
	}
	
	err = retriever.AddMessage(ctx2, msg2)
	if err != nil {
		t.Fatalf("Failed to add message for user 2: %v", err)
	}
	
	// Verify documents were stored with proper user isolation
	if len(mockStore.documents) != 2 {
		t.Fatalf("Expected 2 documents, got %d", len(mockStore.documents))
	}
	
	// Check user isolation in document metadata
	doc1 := mockStore.documents[0]
	doc2 := mockStore.documents[1]
	
	if doc1.Metadata["user_id"] != "user1" {
		t.Errorf("Expected user_id 'user1', got %v", doc1.Metadata["user_id"])
	}
	
	if doc2.Metadata["user_id"] != "user2" {
		t.Errorf("Expected user_id 'user2', got %v", doc2.Metadata["user_id"])
	}
	
	// Verify user_org_key isolation
	if doc1.Metadata["user_org_key"] == doc2.Metadata["user_org_key"] {
		t.Error("User org keys should be different for different users")
	}
}

func TestVectorStoreRetriever_RoleBasedRetrieval(t *testing.T) {
	// Create mock vector store
	mockStore := &MockVectorStore{}
	
	// Create retriever
	retriever := NewVectorStoreRetriever(mockStore)
	
	// Create context
	ctx := context.Background()
	ctx = multitenancy.WithOrgID(ctx, "org1")
	ctx = multitenancy.WithUserID(ctx, "user1")
	ctx = WithConversationID(ctx, "conv1")
	
	// Add messages with different roles
	roles := []string{"user", "assistant", "system"}
	for _, role := range roles {
		msg := interfaces.Message{
			Role:    role,
			Content: fmt.Sprintf("Message from %s", role),
			Metadata: map[string]interface{}{
				"timestamp": time.Now().Unix(),
			},
		}
		
		err := retriever.AddMessage(ctx, msg)
		if err != nil {
			t.Fatalf("Failed to add message for role %s: %v", role, err)
		}
	}
	
	// Test role-based filtering
	messages, err := retriever.GetMessages(ctx,
		interfaces.WithQuery("test query"),
		interfaces.WithRoles("assistant"),
		interfaces.WithLimit(10),
	)
	
	if err != nil {
		t.Fatalf("Failed to get messages: %v", err)
	}
	
	// Verify role filtering (in a real implementation)
	// Note: This is a simplified test - actual filtering would happen in the vector store
	t.Logf("Retrieved %d messages", len(messages))
}

func TestVectorStoreRetriever_EnhancedScoring(t *testing.T) {
	// Create mock vector store
	mockStore := &MockVectorStore{}
	
	// Create retriever
	retriever := NewVectorStoreRetriever(mockStore)
	
	// Create context
	ctx := context.Background()
	ctx = multitenancy.WithOrgID(ctx, "org1")
	ctx = multitenancy.WithUserID(ctx, "user1")
	ctx = WithConversationID(ctx, "conv1")
	
	// Add a message
	msg := interfaces.Message{
		Role:    "assistant",
		Content: "Test message for scoring",
		Metadata: map[string]interface{}{
			"timestamp": time.Now().Unix(),
		},
	}
	
	err := retriever.AddMessage(ctx, msg)
	if err != nil {
		t.Fatalf("Failed to add message: %v", err)
	}
	
	// Test enhanced scoring
	messages, err := retriever.GetMessages(ctx,
		interfaces.WithQuery("test query"),
		interfaces.WithIncludeScore(true),
		interfaces.WithLimit(10),
	)
	
	if err != nil {
		t.Fatalf("Failed to get messages: %v", err)
	}
	
	// Verify enhanced scoring is applied
	for _, msg := range messages {
		if score, ok := msg.Metadata["enhanced_score"]; ok {
			t.Logf("Enhanced score: %v", score)
		} else {
			t.Error("Expected enhanced_score in metadata")
		}
	}
}

func TestVectorStoreRetriever_TimeRangeFiltering(t *testing.T) {
	// Create mock vector store
	mockStore := &MockVectorStore{}
	
	// Create retriever
	retriever := NewVectorStoreRetriever(mockStore)
	
	// Create context
	ctx := context.Background()
	ctx = multitenancy.WithOrgID(ctx, "org1")
	ctx = multitenancy.WithUserID(ctx, "user1")
	ctx = WithConversationID(ctx, "conv1")
	
	// Add messages with different timestamps
	now := time.Now().Unix()
	timestamps := []int64{now - 3600, now - 1800, now} // 1 hour ago, 30 min ago, now
	
	for i, ts := range timestamps {
		msg := interfaces.Message{
			Role:    "user",
			Content: fmt.Sprintf("Message %d", i),
			Metadata: map[string]interface{}{
				"timestamp": ts,
			},
		}
		
		err := retriever.AddMessage(ctx, msg)
		if err != nil {
			t.Fatalf("Failed to add message %d: %v", i, err)
		}
	}
	
	// Test time range filtering
	messages, err := retriever.GetMessages(ctx,
		interfaces.WithQuery("test query"),
		interfaces.WithTimeRange(now-3000, now), // Last 50 minutes
		interfaces.WithLimit(10),
	)
	
	if err != nil {
		t.Fatalf("Failed to get messages: %v", err)
	}
	
	t.Logf("Retrieved %d messages within time range", len(messages))
}
