package memory

import (
	"context"
	"fmt"
	"github.com/google/uuid"
	"github.com/run-bigpig/hongdou/internal/pkg/interfaces"
	"github.com/spf13/cast"
)

// VectorStoreMemory implements a memory that stores messages in a vector store
type VectorStoreMemory struct {
	vectorStore interfaces.VectorStore
}

// MemoryOption represents an option for configuring the vector store retriever
type MemoryOption func(*VectorStoreMemory)

// NewVectorStoreMemory creates a new vector store retriever memory
func NewVectorStoreMemory(vectorStore interfaces.VectorStore, options ...MemoryOption) *VectorStoreMemory {
	retriever := &VectorStoreMemory{
		vectorStore: vectorStore,
	}

	for _, option := range options {
		option(retriever)
	}

	return retriever
}

// AddMessage adds a message to the memory
func (v *VectorStoreMemory) AddMessage(ctx context.Context, message interfaces.Message) error {
	// Store message in vector store
	doc := interfaces.Document{
		ID:      uuid.NewString(),
		Content: message.Content,
		Metadata: map[string]interface{}{
			"role":      message.Role,
			"origin_id": message.OriginId,
			"user_id":   message.UserId,
			"timestamp": message.Metadata["timestamp"],
		},
	}

	if err := v.vectorStore.Store(ctx, []interfaces.Document{doc}); err != nil {
		return fmt.Errorf("failed to store message in vector store: %w", err)
	}

	return nil
}

// GetMessages retrieves messages from the memory
func (v *VectorStoreMemory) GetMessages(ctx context.Context, options ...interfaces.GetMessagesOption) ([]interfaces.Message, error) {
	// Parse options
	opts := &interfaces.GetMessagesOptions{}
	for _, option := range options {
		option(opts)
	}
	if opts.Query == "" {
		return nil, fmt.Errorf("query cannot be empty")
	}
	// Search for relevant messages in vector store
	results, err := v.vectorStore.Search(ctx, opts.Query, opts.Limit, opts.VectorStoreSearchOptions...)
	if err != nil {
		return nil, fmt.Errorf("failed to search vector store: %w", err)
	}

	// Convert search results to messages
	var messages []interfaces.Message
	for _, result := range results {
		role := cast.ToString(result.Document.Metadata["role"])
		timestamp, _ := result.Document.Metadata["timestamp"].(float64)
		messages = append(messages, interfaces.Message{
			Role:    role,
			Content: result.Document.Content,
			Metadata: map[string]interface{}{
				"timestamp": timestamp,
				"score":     result.Score,
			},
		})
	}

	return messages, nil
}

// Clear clears the memory
func (v *VectorStoreMemory) Clear(ctx context.Context) error {
	return nil
}
