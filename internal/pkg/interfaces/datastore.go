package interfaces

import (
	"context"
)

// DataStore represents a generic data storage interface
type DataStore interface {
	// Get retrieves a value by key
	Get(ctx context.Context, key string) (interface{}, error)
	
	// Set stores a value with a key
	Set(ctx context.Context, key string, value interface{}) error
	
	// Delete removes a value by key
	Delete(ctx context.Context, key string) error
	
	// Exists checks if a key exists
	Exists(ctx context.Context, key string) (bool, error)
	
	// List returns all keys matching a pattern
	List(ctx context.Context, pattern string) ([]string, error)
	
	// Close closes the data store connection
	Close() error
}

// DataStoreConfig contains configuration for data stores
type DataStoreConfig struct {
	// Type is the type of data store (e.g., "redis", "memory", "file")
	Type string
	
	// ConnectionString is the connection string for the data store
	ConnectionString string
	
	// Options contains additional configuration options
	Options map[string]interface{}
}
