package interfaces

import (
	"context"
)

// Message represents a message in a conversation
type Message struct {
	// Role is the role of the message sender (e.g., "user", "assistant", "system")
	Role string

	// Content is the content of the message
	Content string

	// Metadata contains additional information about the message
	Metadata map[string]interface{}
}

// Memory represents a memory store for agent conversations
type Memory interface {
	// AddMessage adds a message to memory
	AddMessage(ctx context.Context, message Message) error

	// GetMessages retrieves messages from memory
	GetMessages(ctx context.Context, options ...GetMessagesOption) ([]Message, error)

	// Clear clears the memory
	Clear(ctx context.Context) error
}

// GetMessagesOptions contains options for retrieving messages
type GetMessagesOptions struct {
	// Limit is the maximum number of messages to retrieve
	Limit int

	// Roles filters messages by role
	Roles []string

	// Query is a search query for relevant messages
	Query string
	
	// UserID filters messages by specific user (for admin/cross-user access)
	UserID string
	
	// IncludeScore includes relevance scores in the results
	IncludeScore bool
	
	// MinScore sets minimum relevance score threshold
	MinScore float32
	
	// MemoryType filters by memory type (short_term, long_term, etc.)
	MemoryType string
	
	// TimeRange filters messages by time range
	TimeRange *TimeRange
}

// TimeRange represents a time range filter
type TimeRange struct {
	// Start is the start timestamp (Unix timestamp)
	Start int64
	// End is the end timestamp (Unix timestamp)  
	End int64
}

// GetMessagesOption represents an option for retrieving messages
type GetMessagesOption func(*GetMessagesOptions)

// WithLimit sets the maximum number of messages to retrieve
func WithLimit(limit int) GetMessagesOption {
	return func(o *GetMessagesOptions) {
		o.Limit = limit
	}
}

// WithRoles filters messages by role
func WithRoles(roles ...string) GetMessagesOption {
	return func(o *GetMessagesOptions) {
		o.Roles = roles
	}
}

// WithQuery sets a search query for relevant messages
func WithQuery(query string) GetMessagesOption {
	return func(o *GetMessagesOptions) {
		o.Query = query
	}
}

// WithUserID filters messages by specific user ID
func WithUserID(userID string) GetMessagesOption {
	return func(o *GetMessagesOptions) {
		o.UserID = userID
	}
}

// WithIncludeScore includes relevance scores in the results
func WithIncludeScore(include bool) GetMessagesOption {
	return func(o *GetMessagesOptions) {
		o.IncludeScore = include
	}
}

// WithMinScore sets minimum relevance score threshold
func WithMinScore(score float32) GetMessagesOption {
	return func(o *GetMessagesOptions) {
		o.MinScore = score
	}
}

// WithMemoryType filters by memory type
func WithMemoryType(memoryType string) GetMessagesOption {
	return func(o *GetMessagesOptions) {
		o.MemoryType = memoryType
	}
}

// WithTimeRange filters messages by time range
func WithTimeRange(start, end int64) GetMessagesOption {
	return func(o *GetMessagesOptions) {
		o.TimeRange = &TimeRange{
			Start: start,
			End:   end,
		}
	}
}
