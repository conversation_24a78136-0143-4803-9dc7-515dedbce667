package interfaces

import (
	"context"
)

// Message represents a message in a conversation
type Message struct {
	// OriginId is the ID of the message origin
	OriginId string `json:"origin_id,omitempty"`
	// UserId is the ID of the user who sent the
	UserId string `json:"user_id,omitempty"`
	// Role is the role of the message sender (e.g., "user", "assistant", "system")
	Role string `json:"role,omitempty"`

	// Content is the content of the message
	Content string `json:"content"`

	// Metadata contains additional information about the message
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// Memory represents a memory store for agent conversations
type Memory interface {
	// AddMessage adds a message to memory
	AddMessage(ctx context.Context, message Message) error

	// GetMessages retrieves messages from memory
	GetMessages(ctx context.Context, options ...GetMessagesOption) ([]Message, error)

	// Clear clears the memory
	Clear(ctx context.Context) error
}

// GetMessagesOptions contains options for retrieving messages
type GetMessagesOptions struct {
	VectorStoreSearchOptions []SearchOption
	// Limit is the maximum number of messages to retrieve
	Limit int

	// Roles filters messages by role
	Roles []string

	// Query is a search query for relevant messages
	Query string
}

// GetMessagesOption represents an option for retrieving messages
type GetMessagesOption func(*GetMessagesOptions)

// WithLimit sets the maximum number of messages to retrieve
func WithLimit(limit int) GetMessagesOption {
	return func(o *GetMessagesOptions) {
		o.Limit = limit
	}
}

// WithRoles filters messages by role
func WithRoles(roles ...string) GetMessagesOption {
	return func(o *GetMessagesOptions) {
		o.Roles = roles
	}
}

// WithVectorStoreSearchOptions sets options for vector store search
func WithVectorStoreSearchOptions(options ...SearchOption) GetMessagesOption {
	return func(o *GetMessagesOptions) {
		o.VectorStoreSearchOptions = options
	}
}

// WithQuery sets a search query for relevant messages
func WithQuery(query string) GetMessagesOption {
	return func(o *GetMessagesOptions) {
		o.Query = query
	}
}
