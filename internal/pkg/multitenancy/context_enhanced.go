package multitenancy

import (
	"context"
	"errors"
	"fmt"
)

type contextKey string

const (
	// orgID<PERSON>ey is the context key for the organization ID
	orgIDKey contextKey = "org_id"
	// userIDKey is the context key for the user ID
	userIDKey contextKey = "user_id"
)

var (
	// ErrNoOrgID is returned when no organization ID is found in the context
	ErrNoOrgID = errors.New("no organization ID found in context")
	// ErrNoUserID is returned when no user ID is found in the context
	ErrNoUserID = errors.New("no user ID found in context")
)

// WithOrgID returns a new context with the given organization ID
func WithOrgID(ctx context.Context, orgID string) context.Context {
	return context.WithValue(ctx, orgIDKey, orgID)
}

// GetOrgID returns the organization ID from the context
func GetOrgID(ctx context.Context) (string, error) {
	orgID, ok := ctx.Value(orgIDKey).(string)
	if !ok || orgID == "" {
		return "", ErrNoOrgID
	}
	return orgID, nil
}

// MustGetOrgID returns the organization ID from the context or panics
func MustGetOrgID(ctx context.Context) string {
	orgID, err := GetOrgID(ctx)
	if err != nil {
		panic(err)
	}
	return orgID
}

// HasOrgID returns true if the context has an organization ID
func HasOrgID(ctx context.Context) bool {
	_, err := GetOrgID(ctx)
	return err == nil
}

// WithUserID returns a new context with the given user ID
func WithUserID(ctx context.Context, userID string) context.Context {
	return context.WithValue(ctx, userIDKey, userID)
}

// GetUserID returns the user ID from the context
func GetUserID(ctx context.Context) (string, error) {
	userID, ok := ctx.Value(userIDKey).(string)
	if !ok || userID == "" {
		return "", ErrNoUserID
	}
	return userID, nil
}

// MustGetUserID returns the user ID from the context or panics
func MustGetUserID(ctx context.Context) string {
	userID, err := GetUserID(ctx)
	if err != nil {
		panic(err)
	}
	return userID
}

// HasUserID returns true if the context has a user ID
func HasUserID(ctx context.Context) bool {
	_, err := GetUserID(ctx)
	return err == nil
}

// GetUserOrgKey creates a composite key for user-organization isolation
func GetUserOrgKey(ctx context.Context) (string, error) {
	orgID, err := GetOrgID(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get organization ID: %w", err)
	}
	
	userID, err := GetUserID(ctx)
	if err != nil {
		// If no user ID, use organization-only key for backward compatibility
		return orgID, nil
	}
	
	return fmt.Sprintf("%s:%s", orgID, userID), nil
}

// GetUserOrgKeyWithDefault creates a composite key with default fallbacks
func GetUserOrgKeyWithDefault(ctx context.Context, defaultOrgID, defaultUserID string) string {
	orgID, err := GetOrgID(ctx)
	if err != nil || orgID == "" {
		orgID = defaultOrgID
	}
	
	userID, err := GetUserID(ctx)
	if err != nil || userID == "" {
		if defaultUserID == "" {
			// Return org-only key if no user ID available
			return orgID
		}
		userID = defaultUserID
	}
	
	return fmt.Sprintf("%s:%s", orgID, userID)
}
