package config

import "github.com/zeromicro/go-zero/rest"

type Config struct {
	rest.RestConf
	LLMConfig       LLMConfig
	MasterMind      MasterMind
	VectorStore     VectorStoreConfig
	EmbedderConfig  EmbedderConfig
	RedisConfig     RedisConfig
	McpServerConfig McpServerConfig
}

type VectorStoreConfig struct {
	Weaviate Weaviate
}

type Weaviate struct {
	Host   string
	Schema string
	ApiKey string
}

// 主脑配置
type MasterMind struct {
	Prompt string
	LLM    string
}

type OpenAiConfig struct {
	BaseUrl string
	Model   string
	Sk      string
}

type LLMConfig struct {
	OpenAi OpenAiConfig
}

type EmbedderConfig struct {
	BaseUrl             string
	Model               string
	Sk                  string
	Dimension           int
	SimilarityMetric    string
	SimilarityThreshold float32
}

type RedisConfig struct {
	Addr string
	Pass string
}

type McpServerConfig struct {
	Host string
}
