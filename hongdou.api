syntax = "v1"

type ChatRequest {
	ConversationId string `json:"conversation_id"`
	Message        string `json:"message"`
	Stream         bool   `json:"stream,optional"`
}

type ChatResponse {
	Message string `json:"message"`
}

type StreamResponse {
	Type    string `json:"type"`    // "progress", "result", "error"
	Content string `json:"content"`
	Done    bool   `json:"done"`
}

type SaveMemoryQuest {
     Content string 
}

@server (
	prefix: /api/v1
)
service hongdou {
	@doc (
		summary: "聊天 - Unified endpoint supporting both streaming and non-streaming responses"
	)
	@handler ChatHandler
	post /chat (ChatRequest) returns (ChatResponse)
}

